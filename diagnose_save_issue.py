#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص سريع لمشكلة حفظ القنوات
Quick diagnosis for channels saving issue
"""

import json
import os
import sys
from datetime import datetime

def check_file_status():
    """فحص حالة الملفات"""
    print("🔍 فحص حالة الملفات...")
    
    files_to_check = [
        "data.json",
        "live_tv_app.py",
        "data_temp.json"
    ]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            try:
                stat = os.stat(file_name)
                size = stat.st_size
                modified = datetime.fromtimestamp(stat.st_mtime)
                
                print(f"✅ {file_name}:")
                print(f"   📏 الحجم: {size} بايت")
                print(f"   🕒 آخر تعديل: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # فحص إضافي لملف البيانات
                if file_name == "data.json":
                    try:
                        with open(file_name, "r", encoding="utf-8") as f:
                            data = json.load(f)
                        
                        channels_count = len(data.get("channels", []))
                        daily_code = data.get("daily_code", "غير محدد")
                        
                        print(f"   📺 عدد القنوات: {channels_count}")
                        print(f"   🔐 كود التفعيل: {daily_code}")
                        
                        # عرض أسماء القنوات
                        if channels_count > 0:
                            print("   📋 القنوات المحفوظة:")
                            for i, channel in enumerate(data["channels"][:5]):  # أول 5 قنوات
                                print(f"      {i+1}. {channel.get('name', 'بدون اسم')}")
                            if channels_count > 5:
                                print(f"      ... و {channels_count - 5} قناة أخرى")
                        
                    except json.JSONDecodeError:
                        print("   ❌ خطأ في تنسيق JSON")
                    except Exception as e:
                        print(f"   ❌ خطأ في قراءة البيانات: {e}")
                
            except Exception as e:
                print(f"❌ {file_name}: خطأ في الوصول - {e}")
        else:
            print(f"⚠️ {file_name}: غير موجود")
    
    print()

def check_backup_files():
    """فحص الملفات الاحتياطية"""
    print("🔍 فحص الملفات الاحتياطية...")
    
    backup_files = []
    for file_name in os.listdir("."):
        if file_name.startswith("data_backup_") or file_name.startswith("data_auto_backup_"):
            backup_files.append(file_name)
    
    if backup_files:
        backup_files.sort(reverse=True)  # الأحدث أولاً
        print(f"📁 تم العثور على {len(backup_files)} ملف احتياطي:")
        
        for i, backup_file in enumerate(backup_files[:5]):  # أول 5 ملفات
            try:
                stat = os.stat(backup_file)
                size = stat.st_size
                modified = datetime.fromtimestamp(stat.st_mtime)
                
                print(f"   {i+1}. {backup_file}")
                print(f"      📏 الحجم: {size} بايت")
                print(f"      🕒 التاريخ: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # فحص محتوى الملف الاحتياطي
                try:
                    with open(backup_file, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    channels_count = len(data.get("channels", []))
                    print(f"      📺 عدد القنوات: {channels_count}")
                except:
                    print("      ❌ ملف تالف")
                
            except Exception as e:
                print(f"   ❌ {backup_file}: خطأ - {e}")
        
        if len(backup_files) > 5:
            print(f"   ... و {len(backup_files) - 5} ملف احتياطي آخر")
    else:
        print("⚠️ لا توجد ملفات احتياطية")
    
    print()

def check_permissions():
    """فحص الصلاحيات"""
    print("🔍 فحص صلاحيات الملفات...")
    
    test_file = "permission_test.tmp"
    
    try:
        # اختبار الكتابة
        with open(test_file, "w", encoding="utf-8") as f:
            f.write("test")
        print("✅ صلاحية الكتابة: متوفرة")
        
        # اختبار القراءة
        with open(test_file, "r", encoding="utf-8") as f:
            content = f.read()
        print("✅ صلاحية القراءة: متوفرة")
        
        # اختبار الحذف
        os.remove(test_file)
        print("✅ صلاحية الحذف: متوفرة")
        
    except PermissionError:
        print("❌ لا توجد صلاحيات كافية")
        print("💡 جرب تشغيل البرنامج كمدير")
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
    
    print()

def check_disk_space():
    """فحص مساحة القرص"""
    print("🔍 فحص مساحة القرص...")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        
        total_mb = total // (1024 * 1024)
        used_mb = used // (1024 * 1024)
        free_mb = free // (1024 * 1024)
        
        print(f"💾 إجمالي المساحة: {total_mb:,} MB")
        print(f"📊 المساحة المستخدمة: {used_mb:,} MB")
        print(f"🆓 المساحة المتاحة: {free_mb:,} MB")
        
        if free_mb < 10:
            print("⚠️ تحذير: مساحة القرص منخفضة جداً")
        elif free_mb < 100:
            print("⚠️ تحذير: مساحة القرص منخفضة")
        else:
            print("✅ مساحة القرص كافية")
            
    except Exception as e:
        print(f"❌ خطأ في فحص مساحة القرص: {e}")
    
    print()

def suggest_solutions():
    """اقتراح الحلول"""
    print("💡 الحلول المقترحة:")
    print()
    
    solutions = [
        "🔧 تشغيل البرنامج كمدير (Run as Administrator)",
        "🛡️ إيقاف برنامج مكافحة الفيروسات مؤقتاً",
        "📁 التأكد من وجود مساحة كافية على القرص",
        "🔄 إعادة تشغيل البرنامج",
        "💾 استخدام ملف احتياطي حديث",
        "🧪 تشغيل اختبار حفظ القنوات: python test_channels_save.py",
        "📋 التحقق من ملف data.json يدوياً",
        "🔄 إعادة تثبيت البرنامج في مجلد جديد"
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"{i}. {solution}")
    
    print()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🩺 تشخيص سريع لمشكلة حفظ القنوات")
    print("=" * 60)
    print()
    
    # تشغيل الفحوصات
    check_file_status()
    check_backup_files()
    check_permissions()
    check_disk_space()
    
    # اقتراح الحلول
    suggest_solutions()
    
    print("=" * 60)
    print("📋 ملخص التشخيص:")
    print()
    
    # فحص سريع للمشاكل الشائعة
    issues_found = []
    
    # فحص وجود ملف البيانات
    if not os.path.exists("data.json"):
        issues_found.append("❌ ملف data.json غير موجود")
    else:
        try:
            with open("data.json", "r", encoding="utf-8") as f:
                data = json.load(f)
            if not data.get("channels"):
                issues_found.append("⚠️ لا توجد قنوات في ملف البيانات")
        except:
            issues_found.append("❌ ملف data.json تالف")
    
    # فحص الصلاحيات
    try:
        with open("test_permission.tmp", "w") as f:
            f.write("test")
        os.remove("test_permission.tmp")
    except:
        issues_found.append("❌ مشكلة في صلاحيات الملفات")
    
    # عرض النتائج
    if issues_found:
        print("🚨 المشاكل المكتشفة:")
        for issue in issues_found:
            print(f"   {issue}")
        print()
        print("💡 ابدأ بحل هذه المشاكل أولاً")
    else:
        print("✅ لم يتم اكتشاف مشاكل واضحة")
        print("💡 قد تكون المشكلة في البرنامج نفسه")
    
    print()
    print("🔧 للمساعدة الإضافية:")
    print("   - تشغيل: python test_channels_save.py")
    print("   - مراجعة: TROUBLESHOOTING.md")
    print("   - تشغيل البرنامج من terminal لرؤية رسائل الأخطاء")

if __name__ == "__main__":
    try:
        main()
        input("\n📋 اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء التشخيص")
    except Exception as e:
        print(f"\n❌ خطأ في التشخيص: {e}")
        input("اضغط Enter للخروج...")
