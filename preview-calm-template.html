<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    <title>راية كافية - معاينة القالب بالألوان الهادئة</title>
    
    <!-- Google Fonts -->
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap' rel='stylesheet'/>
    
    <!-- Font Awesome -->
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    
    <!-- Bootstrap CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    
    <!-- AOS Animation Library -->
    <link href='https://unpkg.com/aos@2.3.1/dist/aos.css' rel='stylesheet'/>
    
    <style>
        :root {
            /* ألوان هادئة قابلة للتخصيص */
            --primary-color: #6c7b7f;
            --secondary-color: #95a5a6;
            --accent-color: #3498db;
            --background-color: #f8f9fa;
            --text-color: #2c3e50;
            --card-color: #ffffff;
            --border-radius: 15px;
            --shadow-intensity: 0.1;
            
            /* ألوان مشتقة */
            --primary-light: #b8c5c9;
            --primary-dark: #4a5659;
            --secondary-light: #bdc3c7;
            --accent-light: #85c1e9;
            
            /* تدرجات هادئة */
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-soft: linear-gradient(135deg, var(--background-color) 0%, #ecf0f1 100%);
            --gradient-accent: linear-gradient(45deg, var(--accent-color) 0%, var(--accent-light) 100%);
            
            /* ظلال */
            --shadow-light: 0 4px 15px rgba(0,0,0,0.1);
            --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
            --shadow-heavy: 0 15px 35px rgba(0,0,0,0.2);
            
            /* ألوان حالة */
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --muted-color: #7f8c8d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--background-color);
            overflow-x: hidden;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif;
            color: var(--text-color);
        }

        .hero-section {
            background: var(--gradient-primary);
            background-image: 
                radial-gradient(circle at 30% 70%, var(--primary-light) 0%, transparent 60%),
                radial-gradient(circle at 70% 30%, var(--accent-light) 0%, transparent 60%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            background-size: 200% 200%;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .download-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1.2rem 2.5rem;
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .download-btn:nth-child(1) {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
        }

        .download-btn:nth-child(2) {
            background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
        }

        .download-btn:nth-child(3) {
            background: linear-gradient(45deg, var(--secondary-color), var(--secondary-light));
        }

        .download-btn:hover {
            transform: translateY(-3px);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .stats-section {
            background: var(--gradient-primary);
            padding: 4rem 0;
            color: white;
            position: relative;
        }

        .stat-item {
            text-align: center;
            padding: 2rem 1rem;
            border-radius: var(--border-radius);
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            display: block;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1.2rem;
            opacity: 0.95;
            font-weight: 600;
        }

        .features-section {
            padding: 5rem 0;
            background: var(--gradient-soft);
        }

        .feature-card {
            background: var(--card-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            height: 100%;
            border: 1px solid var(--primary-light);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-medium);
            border-color: var(--accent-color);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .section-title {
            text-align: center;
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: var(--text-color);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--accent-color);
            border-radius: var(--border-radius);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
                gap: 1.5rem;
            }
            
            .download-btn {
                width: 100%;
                max-width: 320px;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class='hero-section'>
        <div class='container'>
            <div class='row justify-content-center'>
                <div class='col-lg-8'>
                    <div class='hero-content' data-aos='fade-up'>
                        <h1 class='hero-title'>راية كافية</h1>
                        <p class='hero-subtitle'>
                            أفضل تطبيق لمشاهدة المباريات المباشرة والقنوات الرياضية مجاناً<br/>
                            استمتع بمتابعة فريقك المفضل بجودة عالية وبدون انقطاع
                        </p>
                        
                        <div class='download-buttons'>
                            <a class='download-btn' href='#'>
                                <i class='fab fa-android'></i>
                                تحميل للأندرويد
                            </a>
                            <a class='download-btn' href='#'>
                                <i class='fas fa-desktop'></i>
                                تحميل للكمبيوتر
                            </a>
                            <a class='download-btn' href='#'>
                                <i class='fas fa-tv'></i>
                                للتلفاز الذكي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class='stats-section'>
        <div class='container'>
            <div class='row'>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up'>
                        <span class='stat-number'>1M+</span>
                        <span class='stat-label'>مستخدم نشط</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='100'>
                        <span class='stat-number'>500+</span>
                        <span class='stat-label'>قناة متاحة</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='200'>
                        <span class='stat-number'>24/7</span>
                        <span class='stat-label'>بث مستمر</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='300'>
                        <span class='stat-number'>4.8★</span>
                        <span class='stat-label'>تقييم المستخدمين</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class='features-section'>
        <div class='container'>
            <h2 class='section-title' data-aos='fade-up'>مميزات التطبيق</h2>
            <div class='row'>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='100'>
                        <div class='feature-icon'>
                            <i class='fas fa-futbol'></i>
                        </div>
                        <h4>مباريات مباشرة</h4>
                        <p>شاهد جميع المباريات المهمة مباشرة بجودة عالية وبدون انقطاع.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='200'>
                        <div class='feature-icon'>
                            <i class='fas fa-tv'></i>
                        </div>
                        <h4>قنوات متنوعة</h4>
                        <p>أكثر من 500 قناة رياضية وترفيهية وإخبارية بجودات مختلفة.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='300'>
                        <div class='feature-icon'>
                            <i class='fas fa-mobile-alt'></i>
                        </div>
                        <h4>متوافق مع جميع الأجهزة</h4>
                        <p>يعمل على الهواتف والأجهزة اللوحية والكمبيوتر والتلفاز الذكي.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    
    <!-- AOS Animation JS -->
    <script src='https://unpkg.com/aos@2.3.1/dist/aos.js'></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>
</body>
</html>
