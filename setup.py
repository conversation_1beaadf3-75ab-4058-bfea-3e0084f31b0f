#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعداد البرنامج
Setup script for Live TV Viewer
"""

import os
import sys
import subprocess
import json
from datetime import datetime

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    requirements = [
        "requests>=2.25.1",
        "pyinstaller>=4.0"  # لإنشاء EXE
    ]
    
    for requirement in requirements:
        try:
            print(f"  تثبيت {requirement}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ {requirement}")
        except subprocess.CalledProcessError:
            print(f"  ❌ فشل في تثبيت {requirement}")
            return False
    
    return True

def create_default_config():
    """إنشاء ملف الإعدادات الافتراضي"""
    print("⚙️ إنشاء ملف الإعدادات...")
    
    config = {
        "app_settings": {
            "title": "مشاهد القنوات المباشرة",
            "version": "1.0",
            "author": "فريق التطوير",
            "window_size": "800x600",
            "theme": "dark"
        },
        "security": {
            "admin_code": "ADMIN2024",
            "daily_code_prefix": "LIVETV",
            "code_length": 8
        },
        "network": {
            "data_url": "https://raw.githubusercontent.com/username/repo/main/data.json",
            "timeout": 10,
            "retry_attempts": 3
        },
        "player": {
            "vlc_paths": [
                "C:\\Program Files\\VideoLAN\\VLC\\vlc.exe",
                "C:\\Program Files (x86)\\VideoLAN\\VLC\\vlc.exe"
            ],
            "activation_interval": 2,
            "auto_start": True
        }
    }
    
    try:
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ تم إنشاء ملف config.json")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
        return False

def verify_data_file():
    """التحقق من ملف البيانات"""
    print("📋 التحقق من ملف البيانات...")
    
    if not os.path.exists("data.json"):
        print("⚠️ ملف data.json غير موجود، سيتم إنشاؤه...")
        
        # إنشاء ملف بيانات افتراضي
        default_data = {
            "daily_code": "TEST123",
            "channels": [
                {
                    "name": "قناة تجريبية",
                    "m3u8_url": "https://example.com/test.m3u8",
                    "activation_base_url": "https://example.com/activate.js?",
                    "start_counter": 1000
                }
            ],
            "last_updated": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        try:
            with open("data.json", "w", encoding="utf-8") as f:
                json.dump(default_data, f, ensure_ascii=False, indent=2)
            print("✅ تم إنشاء ملف data.json افتراضي")
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف البيانات: {e}")
            return False
    else:
        # التحقق من صحة الملف الموجود
        try:
            with open("data.json", "r", encoding="utf-8") as f:
                data = json.load(f)
            
            required_fields = ["daily_code", "channels"]
            for field in required_fields:
                if field not in data:
                    print(f"❌ الحقل المطلوب غير موجود: {field}")
                    return False
            
            print("✅ ملف البيانات صحيح")
        except Exception as e:
            print(f"❌ خطأ في ملف البيانات: {e}")
            return False
    
    return True

def create_shortcuts():
    """إنشاء اختصارات التشغيل"""
    print("🔗 إنشاء اختصارات التشغيل...")
    
    # ملف تشغيل البرنامج
    run_script = """@echo off
title Live TV Viewer
cd /d "%~dp0"
echo ========================================
echo    Live TV Viewer - مشاهد القنوات المباشرة
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo.

python live_tv_app.py

if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل البرنامج
    echo تأكد من تثبيت Python وجميع المتطلبات
    pause
)
"""
    
    # ملف اختبار البرنامج
    test_script = """@echo off
title Test Live TV Viewer
cd /d "%~dp0"
echo ========================================
echo    اختبار برنامج Live TV Viewer
echo ========================================
echo.

python test_app.py

pause
"""
    
    # ملف بناء EXE
    build_script = """@echo off
title Build Live TV Viewer
cd /d "%~dp0"
echo ========================================
echo    بناء ملف EXE للبرنامج
echo ========================================
echo.

python build.py

pause
"""
    
    scripts = [
        ("تشغيل_البرنامج.bat", run_script),
        ("اختبار_البرنامج.bat", test_script),
        ("بناء_EXE.bat", build_script)
    ]
    
    for filename, content in scripts:
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ {filename}")
        except Exception as e:
            print(f"❌ فشل في إنشاء {filename}: {e}")
            return False
    
    return True

def run_tests():
    """تشغيل الاختبارات"""
    print("🧪 تشغيل الاختبارات...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_app.py"
        ], capture_output=True, text=True, encoding="utf-8")
        
        if result.returncode == 0:
            print("✅ جميع الاختبارات نجحت")
            return True
        else:
            print("❌ بعض الاختبارات فشلت")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ فشل في تشغيل الاختبارات: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("🚀 إعداد برنامج Live TV Viewer")
    print("=" * 50)
    print()
    
    steps = [
        ("التحقق من Python", check_python_version),
        ("تثبيت المتطلبات", install_requirements),
        ("إنشاء ملف الإعدادات", create_default_config),
        ("التحقق من ملف البيانات", verify_data_file),
        ("إنشاء اختصارات التشغيل", create_shortcuts),
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"📋 {step_name}...")
        try:
            if not step_function():
                failed_steps.append(step_name)
                print(f"❌ فشل في: {step_name}")
            else:
                print(f"✅ نجح: {step_name}")
        except Exception as e:
            failed_steps.append(step_name)
            print(f"❌ خطأ في {step_name}: {e}")
        print()
    
    # النتائج النهائية
    print("=" * 50)
    if not failed_steps:
        print("🎉 تم إعداد البرنامج بنجاح!")
        print()
        print("📋 الخطوات التالية:")
        print("1. تشغيل البرنامج: python live_tv_app.py")
        print("2. أو استخدام: تشغيل_البرنامج.bat")
        print("3. كود التفعيل التجريبي: TEST123")
        print("4. كود الإدمن: ADMIN2024")
        print()
        
        # اختبار اختياري
        test_choice = input("هل تريد تشغيل الاختبارات الآن؟ (y/n): ").lower().strip()
        if test_choice == 'y':
            run_tests()
        
        return True
    else:
        print("❌ فشل في إعداد البرنامج")
        print("الخطوات التي فشلت:")
        for step in failed_steps:
            print(f"  - {step}")
        print()
        print("يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n❌ اضغط Enter للخروج...")
            sys.exit(1)
        else:
            input("\n✅ اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء الإعداد")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
