#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف بناء البرنامج لإنشاء ملف EXE
Build script for creating EXE file
"""

import os
import sys
import subprocess
import shutil
import time
from datetime import datetime

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("🔧 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def create_exe():
    """إنشاء ملف EXE"""
    print("🏗️ إنشاء ملف EXE...")

    # التحقق من وجود ملف spec المخصص
    spec_file = "LiveTVViewer.spec"

    if os.path.exists(spec_file):
        print(f"📋 استخدام ملف spec المخصص: {spec_file}")
        cmd = [
            "pyinstaller",
            "--clean",                  # تنظيف قبل البناء
            "--noconfirm",              # عدم طلب تأكيد
            "--distpath=dist",          # مجلد الإخراج
            "--workpath=build",         # مجلد العمل
            spec_file                   # ملف spec المخصص
        ]
    else:
        print("📋 استخدام معاملات PyInstaller الافتراضية")
        # معاملات PyInstaller المحسنة
        cmd = [
            "pyinstaller",
            "--onefile",                    # ملف واحد
            "--windowed",                   # بدون نافذة console
            "--name=LiveTVViewer",          # اسم الملف
            "--distpath=dist",              # مجلد الإخراج
            "--workpath=build",             # مجلد العمل
            "--specpath=.",                 # مجلد ملف spec
            "--clean",                      # تنظيف قبل البناء
            "--noconfirm",                  # عدم طلب تأكيد
            "--add-data=data.json;.",       # إضافة ملف البيانات
            "--hidden-import=tkinter",      # استيراد مخفي
            "--hidden-import=tkinter.ttk",  # استيراد مخفي
            "--hidden-import=tkinter.messagebox",  # استيراد مخفي
            "--hidden-import=tkinter.simpledialog",  # استيراد مخفي
            "--hidden-import=requests",     # استيراد مخفي
            "--hidden-import=json",         # استيراد مخفي
            "--hidden-import=threading",    # استيراد مخفي
            "--hidden-import=subprocess",   # استيراد مخفي
            "--hidden-import=os",           # استيراد مخفي
            "--hidden-import=sys",          # استيراد مخفي
            "--hidden-import=time",         # استيراد مخفي
            "--hidden-import=datetime",     # استيراد مخفي
            "--hidden-import=hashlib",      # استيراد مخفي
            "--hidden-import=random",       # استيراد مخفي
            "--hidden-import=string",       # استيراد مخفي
            "live_tv_app.py"               # الملف الرئيسي
        ]

        # إضافة أيقونة إذا كانت متوفرة
        if os.path.exists("icon.ico"):
            cmd.insert(-1, "--icon=icon.ico")

        # إضافة معلومات الإصدار إذا كانت متوفرة
        if os.path.exists("version_info.txt"):
            cmd.insert(-1, "--version-file=version_info.txt")

    try:
        print("⏳ جاري بناء ملف EXE... (قد يستغرق عدة دقائق)")
        print("📝 سيتم عرض تقدم العملية...")

        # تشغيل العملية مع عرض التقدم
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 text=True, universal_newlines=True)

        # عرض التقدم
        for line in process.stdout:
            line = line.strip()
            if line:
                if "INFO:" in line:
                    print(f"ℹ️  {line}")
                elif "WARNING:" in line:
                    print(f"⚠️  {line}")
                elif "ERROR:" in line:
                    print(f"❌ {line}")
                elif "Building" in line or "Analyzing" in line:
                    print(f"🔧 {line}")

        process.wait()

        if process.returncode == 0:
            print("✅ تم إنشاء ملف EXE بنجاح")

            # التحقق من وجود الملف
            exe_path = os.path.join("dist", "LiveTVViewer.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # بالميجابايت
                print(f"📁 مسار الملف: {exe_path}")
                print(f"📏 حجم الملف: {file_size:.1f} MB")

                # اختبار سريع للملف
                print("🧪 اختبار سريع للملف...")
                if test_exe_file(exe_path):
                    print("✅ الملف يعمل بشكل صحيح")
                else:
                    print("⚠️ قد تكون هناك مشاكل في الملف")

                return True
            else:
                print("❌ لم يتم العثور على ملف EXE")
                return False
        else:
            print("❌ فشل في إنشاء ملف EXE")
            return False

    except FileNotFoundError:
        print("❌ PyInstaller غير موجود")
        print("💡 قم بتثبيته باستخدام: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def test_exe_file(exe_path):
    """اختبار سريع لملف EXE"""
    try:
        # تشغيل الملف لثانية واحدة للتأكد من عمله
        process = subprocess.Popen([exe_path], stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        time.sleep(1)
        process.terminate()
        return True
    except Exception:
        return False

def create_portable_package():
    """إنشاء حزمة محمولة"""
    print("📦 إنشاء الحزمة المحمولة...")

    # التحقق من وجود ملف EXE
    exe_path = os.path.join("dist", "LiveTVViewer.exe")
    if not os.path.exists(exe_path):
        print("❌ ملف EXE غير موجود. يجب بناؤه أولاً.")
        return False

    # إنشاء مجلد الحزمة
    package_dir = "LiveTVViewer_Portable"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)

    # نسخ الملفات الأساسية
    files_to_copy = [
        ("dist/LiveTVViewer.exe", "LiveTVViewer.exe"),
        ("data.json", "data.json"),
        ("README.md", "README.md"),
        ("QUICK_START.md", "دليل_البدء_السريع.md")
    ]

    for source_path, dest_name in files_to_copy:
        if os.path.exists(source_path):
            dest_path = os.path.join(package_dir, dest_name)
            shutil.copy2(source_path, dest_path)
            print(f"✅ تم نسخ {source_path} → {dest_name}")
        else:
            print(f"⚠️ الملف غير موجود: {source_path}")

    # إنشاء ملف تشغيل مبسط
    run_exe_content = """@echo off
chcp 65001 >nul
title Live TV Viewer - مشاهد القنوات المباشرة
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        🔴 مشاهد القنوات المباشرة - Live TV Viewer        ██
echo ██                     الإصدار المحمول                      ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.
echo 💡 أكواد الوصول:
echo    - كود التفعيل: TEST123
echo    - كود الإدمن: ADMIN2024
echo.

LiveTVViewer.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من وجود جميع الملفات
    echo    2. تشغيل البرنامج كمدير
    echo    3. إيقاف برنامج مكافحة الفيروسات مؤقتاً
    echo.
    pause
)
"""

    # إنشاء ملف معلومات سريعة
    info_content = """📺 مشاهد القنوات المباشرة - Live TV Viewer
الإصدار المحمول - Portable Version

🔐 أكواد الوصول:
- كود التفعيل: TEST123
- كود الإدمن: ADMIN2024

🚀 طريقة الاستخدام:
1. تشغيل "تشغيل_البرنامج.bat"
2. إدخال كود التفعيل
3. اختيار قناة ومشاهدتها

📋 الملفات المطلوبة:
- LiveTVViewer.exe (البرنامج الرئيسي)
- data.json (بيانات القنوات والأكواد)

🛠️ المتطلبات:
- Windows 7/8/10/11
- اتصال بالإنترنت
- VLC Media Player (اختياري)

📞 للدعم:
راجع ملف "دليل_البدء_السريع.md"

تم إنشاء هذه الحزمة في: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # كتابة الملفات
    with open(os.path.join(package_dir, "تشغيل_البرنامج.bat"), "w", encoding="utf-8") as f:
        f.write(run_exe_content)

    with open(os.path.join(package_dir, "معلومات_البرنامج.txt"), "w", encoding="utf-8") as f:
        f.write(info_content)

    # إنشاء ملف تشغيل إنجليزي
    run_en_content = """@echo off
title Live TV Viewer
echo Starting Live TV Viewer...
LiveTVViewer.exe
if errorlevel 1 pause
"""

    with open(os.path.join(package_dir, "Run_LiveTVViewer.bat"), "w", encoding="utf-8") as f:
        f.write(run_en_content)

    print(f"✅ تم إنشاء الحزمة المحمولة في مجلد: {package_dir}")
    print(f"📁 الملفات المتضمنة:")
    for item in os.listdir(package_dir):
        item_path = os.path.join(package_dir, item)
        if os.path.isfile(item_path):
            size = os.path.getsize(item_path) / 1024  # بالكيلوبايت
            print(f"   📄 {item} ({size:.1f} KB)")

    return True

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["LiveTVViewer.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ تم حذف مجلد: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ تم حذف ملف: {file_name}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🚀 بناء برنامج Live TV Viewer")
    print("=" * 50)
    print()
    
    # التحقق من وجود الملف الرئيسي
    if not os.path.exists("live_tv_app.py"):
        print("❌ الملف الرئيسي غير موجود: live_tv_app.py")
        return False
    
    # تثبيت PyInstaller
    if not install_pyinstaller():
        return False
    
    print()
    
    # إنشاء ملف EXE
    if not create_exe():
        return False
    
    print()
    
    # إنشاء الحزمة المحمولة
    if not create_portable_package():
        return False
    
    print()
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    print()
    print("=" * 50)
    print("🎉 تم بناء البرنامج بنجاح!")
    print("📁 ستجد الحزمة المحمولة في مجلد: LiveTVViewer_Portable")
    print("💡 يمكنك نسخ هذا المجلد إلى أي مكان وتشغيل البرنامج")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في بناء البرنامج")
            input("اضغط Enter للخروج...")
            sys.exit(1)
        else:
            input("\n✅ اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
