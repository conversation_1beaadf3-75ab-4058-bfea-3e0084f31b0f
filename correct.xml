<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:css='false' b:defaultwidgetversion='2' b:layoutsVersion='3' b:responsive='true' expr:dir='data:blog.languageDirection' expr:lang='data:blog.locale' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
  <head>
    <!-- Default Meta -->
    <b:include name='DefaultMeta'/>
    <!-- DNS Prefetech -->
    <b:include name='DNSPrefetech'/>
    <!-- Title -->
    <title><b:if cond='data:view.isError'><data:blog.title.escaped/></b:if><b:if cond='data:view.isMultipleItems'><b:if cond='data:view.isHomepage'><data:blog.title.escaped/><b:else/><data:blog.pageTitle.escaped/></b:if><b:elseif cond='data:view.isSingleItem'/><data:view.title.escaped/></b:if></title>
    <!-- Open Graph -->
    <b:include name='OpenGraph'/>
    <!-- Twitter Card -->
    <b:include name='TwitterCard'/>
    <!-- Feed Links -->
    <b:eval expr='data:blog.feedLinks'/>
    <!-- Required -->
    <meta content='' property='fb:pages'/>
    <meta content='' property='fb:app_id'/>
    <meta content='' property='fb:admins'/>
    <meta content='' name='twitter:site'/>
    <meta content='' name='twitter:creator'/>
    <b:if cond='data:view.isSingleItem'>
      <meta content='' property='article:publisher'/> 
      <meta content='' property='article:author'/>
    </b:if>

<b:if cond='!data:view.isLayoutMode'><b:skin version='1.0.0'><![CDATA[
/*
~> Name      : ThemeX Sport
~> Designer  : ThemeX
~> Developers: https://themex.store
~> Version	 : 2.8
~> Updated	 : 16-1-2024
*/
/*
<!-- Constants -->
<Variable name="keycolor" description="Main Color" type="color" default="$(main.color)" value="#0066cc"/>
<Group description="شكل القالب" selector="body">
<Variable name="body.background.color" description="Background Color" type="color" default="#e8e8e8" value="#e8e8e8"/>
<Variable name="body.background" description="body.background" type="background" color="$(body.background.color)" default="$(color) none no-repeat fixed center center" value="$(color) none no-repeat fixed center center"/> 
  <Variable name="main.color" description="main color" type="color" default="#0066cc"  value="#0066cc"/>
  <Variable name="body.text.color" description="Text Color" type="color" default="#333"  value="#333333"/>
  <Variable name="body.link.color" description="Link Color" type="color" default="$(main.color)"  value="#0066cc"/>
</Group>
<Group description='بعض الاعدادات' selector="body">
<Variable name="HideComment" description="اخفاء التعليقات" type="length" default="1px" min="1px" max="2px" value="2px"/>
<Variable name="HideRelated" description="اخفاء ذات صلة" type="length" default="1px" min="1px" max="2px" value="1px"/>
<Variable name="BlogFonts" description="الخط" type="length" default="1px" min="1px" max="3px" value="2px"/>
<Variable name="TOPfooter" description="اخفاء فوق الذيل" type="length" default="1px" min="1px" max="2px" value="1px"/>
<Variable name="StyleSite" description="شكل الموقع" type="length" default="1px" min="1px" max="2px" value="1px"/>
<Variable name="AuthorN" description="اخفاء اسم كاتب المقالة" type="length" default="1px" min="1px" max="2px" value="2px"/>

</Group>
<!-- Extra Variables -->
<Variable name="body.text.font" description="Font" hideEditor="true" type="font" default="14px TajASWal, sans-serif"  value="14px TajASWal, sans-serif"/>
<Variable name="posts.background.color" description="Post background color" hideEditor="true" type="color" default="#ffffff"  value="#ffffff"/>
<Variable name="tabs.font" description="Font 2" hideEditor="true" type="font" default="14px TajASWal, sans-serif"  value="14px TajASWal, sans-serif"/>
<Variable name="posts.title.color" description="Post title color" hideEditor="true" type="color" default="#070707"  value="#070707"/>
<Variable name="posts.text.color" description="Post text color" hideEditor="true" type="color" default="#656565"  value="#656565"/>
<Variable name="posts.icons.color" description="Post icons color" hideEditor="true" type="color" default="$(main.color)"  value="#0066cc"/>
<Variable name="labels.background.color" description="Label background color" hideEditor="true" type="color" default="$(main.color)"  value="#0066cc"/>
*/
  
/*================= Icons Svg ===================*/
.IconA-tumblr {background: no-repeat center url("data:image/svg+xml;charset=utf8,%3Csvg aria-hidden='true' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512' %3E%3Cpath fill='%23fff' d='M309.8 480.3c-13.6 14.5-50 31.7-97.4 31.7-120.8 0-147-88.8-147-140.6v-144H17.9c-5.5 0-10-4.5-10-10v-68c0-7.2 4.5-13.6 11.3-16 62-21.8 81.5-76 84.3-117.1.8-11 6.5-16.3 16.1-16.3h70.9c5.5 0 10 4.5 10 10v115.2h83c5.5 0 10 4.4 10 9.9v81.7c0 5.5-4.5 10-10 10h-83.4V360c0 34.2 23.7 53.6 68 35.8 4.8-1.9 9-3.2 12.7-2.2 3.5.9 5.8 3.4 7.4 7.9l22 64.3c1.8 5 3.3 10.6-.4 14.5z' cclass=''%3E%3C/path%3E%3C/svg%3E");}.IconA-whatsapp {background: no-repeat center url("data:image/svg+xml;charset=utf8,%3Csvg aria-hidden='true' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' %3E%3Cpath fill='%23fff' d='M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z' cclass=''%3E%3C/path%3E%3C/svg%3E");}.IconA-youtube {background: no-repeat center url("data:image/svg+xml;charset=utf8,%3Csvg aria-hidden='true' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 576 512' %3E%3Cpath fill='%23fff' d='M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z' cclass=''%3E%3C/path%3E%3C/svg%3E");}.IconA-telegram {background: no-repeat center url("data:image/svg+xml;charset=utf8,%3Csvg aria-hidden='true' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' %3E%3Cpath fill='%23fff' d='M446.7 98.6l-67.6 318.8c-5.1 22.5-18.4 28.1-37.3 17.5l-103-75.9-49.7 47.8c-5.5 5.5-10.1 10.1-20.7 10.1l7.4-104.9 190.9-172.5c8.3-7.4-1.8-11.5-12.9-4.1L117.8 284 16.2 252.2c-22.1-6.9-22.5-22.1 4.6-32.7L418.2 66.4c18.4-6.9 34.5 4.1 28.5 32.2z' cclass=''%3E%3C/path%3E%3C/svg%3E");}.IconA-instagram {background: no-repeat center url("data:image/svg+xml;charset=utf8,%3Csvg aria-hidden='true' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' %3E%3Cpath fill='%23fff' d='M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z' cclass=''%3E%3C/path%3E%3C/svg%3E");}.IconA-facebook {background: no-repeat center url("data:image/svg+xml,%0A%3Csvg aria-hidden='true' focusable='false' data-prefix='fab' data-icon='facebook-f' class='svg-inline--fa fa-facebook-f fa-w-10' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='%23fff' d='M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z'%3E%3C/path%3E%3C/svg%3E");}.IconA-twitter {background: no-repeat center url("data:image/svg+xml,%0A%3Csvg aria-hidden='true' focusable='false' data-prefix='fab' data-icon='twitter' class='svg-inline--fa fa-twitter fa-w-16' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23fff' d='M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z'%3E%3C/path%3E%3C/svg%3E");}.IconA-playgoogle {background: no-repeat center url("data:image/svg+xml,%0A%3Csvg aria-hidden='true' focusable='false' data-prefix='fab' data-icon='google-play' class='svg-inline--fa fa-google-play fa-w-16' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23ffffff' d='M325.3 234.3L104.6 13l280.8 161.2-60.1 60.1zM47 0C34 6.8 25.3 19.2 25.3 35.3v441.3c0 16.1 8.7 28.5 21.7 35.3l256.6-256L47 0zm425.2 225.6l-58.9-34.1-65.7 64.5 65.7 64.5 60.1-34.1c18-14.3 18-46.5-1.2-60.8zM104.6 499l280.8-161.2-60.1-60.1L104.6 499z'%3E%3C/path%3E%3C/svg%3E");}.IconA-news {background-image: url("data:image/svg+xml,%3Csvg version='1.0' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500.000000 500.000000' preserveAspectRatio='xMidYMid meet'%3E%3Cg transform='translate(0.000000,500.000000) scale(0.100000,-0.100000)'%0Afill='%23ffffff' stroke='none'%3E%3Cpath d='M825 3950 c-54 -10 -82 -22 -133 -60 -52 -37 -97 -126 -112 -216 -14%0A-83 -13 -1899 1 -1984 44 -277 183 -439 449 -522 l75 -23 1480 0 c1425 0 1482%0A1 1535 19 149 51 218 108 272 222 21 46 43 106 48 135 6 32 10 408 10 975 l0%0A924 -29 32 c-29 32 -31 33 -132 38 l-102 5 -14 55 c-26 103 -88 217 -156 285%0A-76 75 -125 102 -214 115 -79 11 -2915 12 -2978 0z m202 -245 c12 -8 27 -32%0A32 -52 7 -25 11 -352 11 -966 0 -1008 -2 -969 55 -995 14 -7 46 -12 70 -12 35%0A0 52 6 77 29 l33 29 3 942 c2 787 0 950 -12 996 l-14 54 1222 0 c1345 0 1288%0A3 1360 -61 38 -33 86 -120 86 -156 l0 -23 -1156 0 -1156 0 -34 -34 c-34 -34%0A-34 -34 -35 -132 0 -55 -1 -439 -2 -854 -2 -753 -2 -755 -24 -821 -13 -36 -41%0A-93 -62 -127 -33 -50 -51 -67 -99 -91 -123 -62 -261 -64 -380 -5 -108 52 -166%0A135 -191 269 -7 39 -11 366 -11 989 0 898 1 933 20 974 10 23 20 42 22 42 2 0%0A21 7 43 15 45 17 108 12 142 -10z m3191 -1292 l-3 -838 -24 -53 c-17 -38 -37%0A-62 -69 -84 -88 -61 -30 -58 -1289 -58 -634 0 -1153 3 -1153 7 0 3 13 27 29%0A52 35 54 54 103 78 191 15 58 17 148 20 843 l4 777 1205 0 1204 0 -2 -837z'/%3E%3Cpath d='M2040 2805 l0 -225 60 0 59 0 3 123 3 122 35 -49 c19 -26 43 -58 54%0A-70 10 -11 30 -45 43 -74 l26 -53 66 3 c45 2 66 7 65 16 -1 6 -2 107 -2 222%0Al-2 210 -60 0 -59 0 -3 -132 -3 -133 -24 45 c-13 25 -50 84 -83 133 l-59 87%0A-59 0 -60 0 0 -225z'/%3E%3Cpath d='M2557 3023 c-4 -3 -7 -105 -7 -225 l0 -218 186 0 185 0 -3 48 -3 47%0A-113 3 -113 3 3 42 3 42 98 3 97 3 0 39 0 40 -100 0 -100 0 0 40 0 39 108 3%0A107 3 3 48 3 47 -174 0 c-95 0 -177 -3 -180 -7z'/%3E%3Cpath d='M2960 3018 c0 -7 21 -109 47 -225 l47 -213 61 0 62 0 22 83 c41 154%0A53 189 61 180 7 -7 20 -47 64 -205 l17 -58 64 0 c52 0 65 3 69 18 16 53 58%0A257 66 322 6 41 13 83 16 93 5 16 0 18 -53 15 l-58 -3 -24 -129 c-14 -77 -27%0A-123 -31 -115 -5 8 -16 43 -25 79 -9 36 -23 89 -31 118 l-15 52 -62 0 -62 0%0A-29 -112 c-16 -62 -32 -119 -36 -126 -3 -7 -12 23 -19 65 -7 43 -17 99 -23%0A126 l-10 47 -59 0 c-40 0 -59 -4 -59 -12z'/%3E%3Cpath d='M3684 3016 c-49 -21 -74 -61 -74 -117 0 -67 33 -98 144 -133 46 -15%0A90 -34 96 -41 18 -21 8 -48 -22 -63 -37 -17 -77 -5 -97 30 -15 27 -19 28 -76%0A26 -57 -3 -60 -4 -57 -28 3 -34 32 -71 73 -95 46 -27 188 -28 236 -2 39 21 73%0A82 73 130 0 74 -50 113 -193 152 -54 15 -58 18 -55 43 3 24 8 27 44 30 35 3%0A45 -1 62 -22 17 -22 28 -26 72 -26 l53 0 -6 36 c-7 44 -23 63 -64 81 -41 17%0A-169 16 -209 -1z'/%3E%3Cpath d='M3110 1980 l0 -380 78 0 77 0 0 93 c0 119 22 165 95 202 27 14 56 25%0A64 25 11 0 8 11 -12 48 -68 121 3 282 125 282 120 0 194 -163 128 -279 -14%0A-23 -25 -44 -25 -46 0 -2 12 -6 26 -9 14 -4 44 -18 66 -32 57 -37 81 -95 80%0A-201 l0 -83 79 0 79 0 0 380 0 380 -430 0 -430 0 0 -380z'/%3E%3Cpath d='M2070 2320 c-25 -25 -26 -61 -1 -91 l19 -24 364 -3 c199 -2 378 0%0A396 3 38 7 72 40 72 70 0 12 -10 31 -23 43 -23 22 -27 22 -415 22 -379 0 -393%0A-1 -412 -20z'/%3E%3Cpath d='M2076 2039 c-19 -15 -26 -30 -26 -54 0 -24 7 -39 26 -54 26 -20 37%0A-21 411 -21 l384 0 24 25 c14 13 25 36 25 50 0 14 -11 37 -25 50 l-24 25 -384%0A0 c-374 0 -385 -1 -411 -21z'/%3E%3Cpath d='M2076 1759 c-19 -15 -26 -30 -26 -54 0 -24 7 -39 26 -54 26 -20 37%0A-21 411 -21 l384 0 24 25 c14 13 25 36 25 50 0 14 -11 37 -25 50 l-24 25 -384%0A0 c-374 0 -385 -1 -411 -21z'/%3E%3C/g%3E%3C/svg%3E%0A");}.IconA-telegram {background: no-repeat center url("data:image/svg+xml,%3Csvg fill='%23fff' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 496 512'%3E%3Cpath d='M248,8C111.033,8,0,119.033,0,256S111.033,504,248,504,496,392.967,496,256,384.967,8,248,8ZM362.952,176.66c-3.732,39.215-19.881,134.378-28.1,178.3-3.476,18.584-10.322,24.816-16.948,25.425-14.4,1.326-25.338-9.517-39.287-18.661-21.827-14.308-34.158-23.215-55.346-37.177-24.485-16.135-8.612-25,5.342-39.5,3.652-3.793,67.107-61.51,68.335-66.746.153-.655.3-3.1-1.154-4.384s-3.59-.849-5.135-.5q-3.283.746-104.608,69.142-14.845,10.194-26.894,9.934c-8.855-.191-25.888-5.006-38.551-9.123-15.531-5.048-27.875-7.717-26.8-16.291q.84-6.7,18.45-13.7,108.446-47.248,144.628-62.3c68.872-28.647,83.183-33.623,92.511-33.789,2.052-.034,6.639.474,9.61,2.885a10.452,10.452,0,0,1,3.53,6.716A43.765,43.765,0,0,1,362.952,176.66Z'/%3E%3C/svg%3E");}

/*==========> Normalize <===========*/
ul{margin:0;padding:0}li{list-style: none;}*{text-decoration:none;margin:0;padding:0;outline:0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,abbr,acronym,address,big,cite,code,del,dfn,em,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{border:0;font-family:inherit;font-size:100%;font-style:inherit;color:inherit;font-weight:inherit;margin:0;outline:0;padding:0;vertical-align:baseline}img{max-height:100%;max-width:100%;position:relative}body,input{font:400 15px 'Segoe UI'}::selection {background: #3f6af4;text-shadow: none;color: #fff;}a{color:var(--CO);text-decoration:none;background-color:transparent;-webkit-text-decoration-skip:objects}.none,[none],header .headline,footer .headline,div#ADS {display: none!important;}
/*==========> Root <===========*/
:root{--Loding:url("data:image/svg+xml,%0A%3Csvg width='200px' height='200px' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 100 100' preserveAspectRatio='xMidYMid' style='background: none;'%3E%3Ccircle cx='75' cy='50' fill='%23363a3c' r='6.39718'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.875s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='67.678' cy='67.678' fill='%23363a3c' r='4.8'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.75s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='50' cy='75' fill='%23363a3c' r='4.8'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.625s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='32.322' cy='67.678' fill='%23363a3c' r='4.8'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.5s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='25' cy='50' fill='%23363a3c' r='4.8'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.375s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='32.322' cy='32.322' fill='%23363a3c' r='4.80282'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.25s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='50' cy='25' fill='%23363a3c' r='6.40282'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='-0.125s'%3E%3C/animate%3E%3C/circle%3E%3Ccircle cx='67.678' cy='32.322' fill='%23363a3c' r='7.99718'%3E%3Canimate attributeName='r' values='4.8;4.8;8;4.8;4.8' times='0;0.1;0.2;0.3;1' dur='1s' repeatCount='indefinite' begin='0s'%3E%3C/animate%3E%3C/circle%3E%3C/svg%3E");--BG:$(main.color);--BT:#eceef2;--BTR:#d8dbe1;--BG1:$(body.background);--BG2:#fff;--BG3:#fff;--CO:#333;--BR:#eee;--BP:#eee;--GD:#eceef2;--BL:#e8e8e8;--BFO:#fff;--BGS:$(main.color);}
:root body.Dark{--BG:#1c2733;--BT:#1c2733;--BG1:#1c2733;--BTR:#374656;--BG2:#263545;--BG3:#121a23;--CO:#fff;--BR:#252f3a;--BP:#1c2733;--GD:#121a23;--BFO:#263545;--BL:#121a23;--BGS:#121a23;}
/*================= Body ===================*/
body{background:var(--BG1);position: relative;font-size: 14px!important;overflow-x: hidden;line-height: 1.5;color:var(--CO);}ul.socialAS {display: flex;gap: 0px 10px;}ul.socialAS li,ul.socialAS li a{display:inline-block}ul.socialAS li a span[class*='IconA']{width:35px;height:35px;padding:4px;background-color:#444;border-radius:10px;color:#fff;display:inline-block;background-size:55% 55%;}ul.socialAS li a .IconA-facebook{background-color:#3b5998!important}ul.socialAS li a .IconA-twitter{background-color:#1da1f2!important}ul.socialAS li a .IconA-whatsapp{background-color:#52b033!important}ul.socialAS li a .IconA-youtube{background-color:#e52d27!important}ul.socialAS li a .IconA-telegram{background-color:#216a86!important}ul.socialAS li a .IconA-pinterest{background-color:#cc2127!important}span.IconA-news {background-size: 100% 95%!important;background-color:#d60f00!important}

/*====== header ======*/
header>div{max-width:1100px;margin:0 auto}header .Logo div#Header1 img{max-width:100%;width:200px;height:auto}header{position:relative;padding:10px;margin:auto;background:var(--BG2);box-shadow:0 1px 7px #1010164a;overflow:hidden;margin-bottom:10px}header .Logo div#Header1{width:200px;text-align:center;overflow:hidden;margin:0 auto}header .Logo{text-align:center;display:grid;justify-content:center;align-items:center;float:right;margin:0 0 0 20px}.Menu div#LinkList2 ul li a{background:var(--BG);padding:7px 10px;font-size:16px;font-weight:700;border-radius:5px;color:#fff;display:grid;justify-content:center;align-items:center;}div#Menut div#LinkList8 {float: left;}.Menu div#LinkList2{display: inline-block;}.Menu div#LinkList2 ul li{display:inline-block;margin:0 5px}.Menu div#LinkList2 ul li:first-child {display: none;}.Menu{display:grid;justify-content:center;align-items:center;height:57px;grid-template-columns:8fr 1fr}main#main .post-filter-message{background:#fff;border-radius:10px 10px 0 0;padding:10px;box-shadow:0 -2px 5px #1010160f;border:1px solid #eee;text-align:center}main#main .post-filter-message>a{display:inline-block;background:var(--BG);color:#fff;padding:1px 8px;border-radius:3px;margin:0 7px 0 0}main#main .post-filter-message>div{display:inline-block}main#main .post-filter-message>div span{color:#111;font-weight:700;margin:0 4px;display:inline-block}main#main .post-filter-message>div{display:inline-block;font-size:16px}header .MenuX {display: none;}header .MenuX svg g {stroke: #333;}.Dark header .MenuX svg g {stroke: #fff;}body.StyleSite1 .Menu div#LinkList2 ul li a:hover:after {background: var(--BG);content: '';display: block;width: 100%;height: 4px;position: absolute;bottom: 0;left:0;right: 0;border-radius: 10px;}body.StyleSite1 .Menu div#LinkList2 ul li a {background: none;color: var(--CO)!important;position: relative;}body.StyleSite1 .containerMatch a ul {display: none!important;}
div#ADSTOPTable {margin: 0 auto 10px auto;}
body.Table div#ADSTOPTable{margin: 10px auto}
/*====== Post In Home ======*/
.ASW.video .Image>a {position: relative;height: 100%;display: block;}.ASW.video .Image>a:after {transition: all .3s;position: absolute;content: '';display: block;top: 10px;left: 10px;background-image: url("data:image/svg+xml,%0A%3Csvg fill='%23fff' xmlns='http://www.w3.org/2000/svg' enable-background='new 0 0 32 32' viewBox='0 0 32 32'%3E%3Cpath d='M16,0.5C7.45313,0.5,0.5,7.45313,0.5,16S7.45313,31.5,16,31.5S31.5,24.54688,31.5,16S24.54688,0.5,16,0.5z M16,28.5C9.10742,28.5,3.5,22.89258,3.5,16S9.10742,3.5,16,3.5S28.5,9.10742,28.5,16S22.89258,28.5,16,28.5z'/%3E%3Cpath d='M23.54004,14.76465L13.50586,7.85352c-0.45898-0.31641-1.05371-0.35156-1.54883-0.09277c-0.49316,0.25977-0.80176,0.77051-0.80176,1.32813v13.82227c0,0.55762,0.30859,1.06836,0.80176,1.32813c0.21973,0.11523,0.45898,0.17188,0.69824,0.17188c0.29883,0,0.5957-0.08887,0.85059-0.26465l10.03418-6.91113c0.40625-0.28027,0.64941-0.74219,0.64941-1.23535S23.94629,15.04492,23.54004,14.76465z M14.15527,20.05664v-8.11328L20.04492,16L14.15527,20.05664z'/%3E%3C/svg%3E");background-repeat: no-repeat; background-size: 100%;width: 30px;height: 30px;background-position: center;box-shadow: 0px 0px 13px #1727554a;border-radius: 50%;}.ASW.video .Image:hover a:after {top: 37%;left: 39%;width: 55px;height: 55px;box-shadow: 0px 0px 20px 10px #17275536:}div#ADSTopPost div#HTML14 {position: fixed;top: 52%;left: 10px;right: 10px;z-index: 11111;display: flex;justify-content: center;align-items: center;}div#HTML14 span.CloseAdsUp svg {position: absolute;top: -30px;background: red;fill: #fff;color: #fff;width: 28px;height: auto;border-radius: 50%;right: 40px;cursor: pointer;}main#main div#page_body,.ASW{max-width:1100px;margin:0 auto;background:var(--BG2);border-radius:0 0 10px 10px;padding:10px;box-shadow:0 1px 7px #1010164a;}main#main,div#PostsEnd,div#PostsTop,.TabulaSoprt{max-width:1100px;margin:0 auto 10px}main#main{width:96%}div#PostsEnd .widget,div#PostsTop .widget{margin:0 0 10px}body.multipleItems .blog-posts.hfeed.container{display:grid;grid-template-columns:repeat(4,1fr);gap:7px 15px;justify-content:center;align-items:center;justify-items:center;align-content:center;position:relative;padding:10px}body.multipleItems .ASW .Posts .item,.Related .Posts .item,body.multipleItems article.Item{display:inline-block;z-index:1;border-radius:7px;transition:.3s all ease;position:relative;margin-bottom:10px;overflow:hidden;width:100%;background:var(--BP);box-shadow:0 0 2px 0 #0a0d3714}body.multipleItems .ASW .Posts .item .Image,.Related .Posts .item .Image,.PostImage{height:170px}body.multipleItems .ASW .Posts .item .Image a img,.Related .Posts .item .Image a img,.PostImage a img{height:100%;width:100%;transition:.25s all ease;object-fit:cover}body.multipleItems .Title,.Related .Posts .Title{grid-template-columns: 1fr;font-size:15px;text-align:center;padding:3px 0 0 0;color:var(--CO);transition:.3s .1s all ease;line-height:21px;width:100%;display:grid;justify-content:center;align-items:center;height:44px;min-height:44px;max-height:44px}body.multipleItems ul.labels li.live a {    animation: blinker 500ms ease-in-out infinite;background: red;padding: 1px 9px;}body.multipleItems ul.labels li.live {display: block!important;}.PostImage a{display:block;width:100%;height:100%}.Title a{font-weight:600;color:var(--CO);display: block;}body.multipleItems ul.labels{position:absolute;top:5px;right:3px}body.multipleItems ul.labels li a{color:#fff;font-weight:500;background:var(--BG);padding:3px 2px;border-radius:5px;text-align:center;display:block}body.multipleItems ul.labels li{margin:0 5px 5px 0;text-align:center}body.multipleItems div.Posts,.Related .Posts{margin:10px auto;display:grid;grid-template-columns:repeat(4,1fr);gap:7px 15px;justify-content:center;align-items:center;justify-items:center;align-content:center;max-width:1100px;position:relative;padding:10px}.headline h3.title{font-size:20px;font-weight:700;display:inline-block;margin:0 auto}.headline{position:relative;border-radius:10px 10px 0 0;background:var(--BG3);color:var(--CO);padding:10px;box-shadow:0 -2px 7px #10101614;border:1px solid var(--BR)}.headline a.Lapel-Link{text-align:center;font-size:14px;cursor:pointer;direction:rtl;float:left;background:var(--BR);border-radius:10px;color:var(--CO);padding:6px 10px;display:grid;justify-content:center;align-items:center;font-weight:700}.headline a.Lapel-Link:hover{background:var(--BG1);color:var(--CO)}div#tabulaSoprt .headline h3{all:inherit}div#tabulaSoprt .headline{all:inherit}.TabulaSoprt{max-width:1100px;margin:0 auto 25px;background:var(--BG2);border-radius:10px;box-shadow:0 1px 7px #1010164a;border:1px solid var(--BR)}.TabulaSoprt div#tabulaSoprt{padding:10px}.TabulaSoprt .TabulaGo{padding:10px 10px 5px;overflow:hidden;border-bottom:2px solid var(--BG3);border-radius: 10px 10px 0px 0px;background: var(--BG3);}.TabulaSoprt .TabulaGo h2.boxstitle{font-weight: 700;display:inline-block;float:right;font-size:20px}.TabulaSoprt .TabulaGo h2.boxstitle:before{display:inline-block;width:25px;height:25px;margin-left:10px;background:url("data:image/svg+xml;charset=utf8,%3Csvg aria-hidden='true' class='svg-inline--fa fa-futbol fa-w-16' data-icon='futbol' data-prefix='fad' focusable='false' role='img' style='--fa-secondary-opacity: 0.1;' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg'%3E%3Cg class='fa-group'%3E%3Cpath class='fa-secondary' d='M452 104v-.05.05zm51.88 159.77A246.7 246.7 0 0 1 461 395.61l-7.15-31.45-109.5 13.43-46.69 100.1L325 494l.06.23a248.87 248.87 0 0 1-138.06 0V494l27.37-16.28-46.69-100.1-109.52-13.46L51 395.61a246.7 246.7 0 0 1-42.87-131.8l24.17 21.08 80.61-75.24-20.83-108.5L60 104a248.5 248.5 0 0 1 111.65-81.26l.35.26-12.4 29.11 96.4 53.41 96.4-53.41-12.65-29.6A248.6 248.6 0 0 1 452 104l-31.7-2.84-21.16 108.5 80.62 75.24zM356.32 228L256 155.33 155.68 228l38.45 117.44h124.09z' fill='%23eee'%3E%3C/path%3E%3Cpath class='fa-primary' d='M352.4 52.11l-12.65-29.6a249 249 0 0 0-167.5 0l-12.65 29.6 96.4 53.41zm-34.18 293.37L356.32 228 256 155.33 155.68 228l38.45 117.44zM92.08 101.15L60 104A246.92 246.92 0 0 0 8 256c0 2.61.05 5.21.13 7.81l24.17 21.08 80.61-75.24zm252.26 276.44l-46.69 100.1 27.69 16.47A248.45 248.45 0 0 0 461 395.61l-7.15-31.45zM58.16 364.16L51 395.61a248.45 248.45 0 0 0 135.65 98.55l27.69-16.47-46.69-100.1zM452 104l-31.7-2.84-21.16 108.5 80.62 75.24 24.16-21.08c.08-2.6.13-5.2.13-7.81A246.92 246.92 0 0 0 452 104z' fill='%230b294c'%3E%3C/path%3E%3C/g%3E%3C/svg%3E") center no-repeat;position:relative;top:5px;content:'';background-position:center;background-repeat:no-repeat;background-size:100% 100%}.TabulaSoprt .TabulaGo ul.nav-tabs{display:grid;float:left;grid-template-columns:1fr 1fr 1fr;justify-content:center;align-items:center;text-align:center;gap:0 10px}.TabulaSoprt .TabulaGo ul.nav-tabs a{padding: 3px 10px;display:block;border-radius:5px;cursor:pointer;font-weight:600;color:#fff}.containerMatch a {color: #111;}.TabulaSoprt .TabulaGo ul.nav-tabs a.yesterday{    background: #104783;}.TabulaSoprt .TabulaGo ul.nav-tabs a.Today{    background: #931800;}.TabulaSoprt .TabulaGo ul.nav-tabs a.tomorrow{    background: #caa107;}.TabulaSoprt .TabulaGo ul.nav-tabs li {border-radius: 5px;color: #fff;cursor: pointer;}li.yesterday {background: #1778f2;}li.Today {background: #e8353c;}li.tomorrow {background: #111;}.containerMatch{padding: 0;border: 1px solid var(--BG2);overflow: hidden;text-align: center;background:var(--GD);margin-bottom: 15px;position: relative;border-radius: 10px;}.Today,Yesterday,Tomorrow {display: flex;flex-direction: column;}.containerMatch.Live {order: 1;}.containerMatch.Soon {order: 2;}.containerMatch.Not {order: 3;}.containerMatch.End{order: 4;}.containerMatch a .Match{display:grid;justify-content:center;align-items:center;grid-template-columns:repeat(3,1fr)}.containerMatch a ul{display:grid;justify-content:center;align-items:center;grid-template-columns:repeat(3,1fr);border-top:2px solid #ccc;padding:2px 5px;margin:5px 0 0}.containerMatch a ul li{list-style:none;font-size:15px;font-weight:600;color:var(--CO);padding:3px}.containerMatch a ul li:nth-child(1):before {content: "📺";font-family: FontAwesome;display: inline-flex;margin: 0 0 0 2px;}.containerMatch a ul li:nth-child(2):before {content: "🎤";margin-left: 8px;font-family: FontAwesome;color: #000;}.containerMatch a ul li:nth-child(3):before {content: "🏆";font-family: FontAwesome;color: #eaae19;margin: 0 0 0 3px;}@keyframes blinker{0%{background-color: #c00606;}50%{background-color: #4b0a03;}100%{background-color: #c00606;}}.containerMatch a .Match .natej,.containerMatch a .matchHour{margin:0 0 10px;font-weight:600;font-size:18px;color:var(--CO);}.containerMatch a .Match .natej {letter-spacing: 8px;}.containerMatch a .Match .fareq{padding:10px}.asm{font-size:19px;font-weight:700;color:var(--CO)}.containerMatch a .Show{position:absolute;z-index:2;background:#12131840;top:0;left:0;right:0;width:100%;height:100%;background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM10.622 8.415l4.879 3.252a.4.4 0 0 1 0 .666l-4.88 3.252a.4.4 0 0 1-.621-.332V8.747a.4.4 0 0 1 .622-.332z' fill='rgba(255,255,255,1)'/%3E%3C/svg%3E");background-size:6% 37%;background-repeat:no-repeat;background-position:center;opacity:0;transition:opacity .1s ease-in-out}.containerMatch:hover .Show{opacity:1}.Match .matchDate.end {background-color: #111;padding: 4px 10px;border-radius: 5px;color: #fff;display: inline-block;font-size: 16px;}.Match .matchDate.live {    animation: blinker 500ms ease-in-out infinite;background-color: #d62c1a;padding: 4px 10px;border-radius: 5px;color: #fff;display: inline-block;font-size: 16px;}.Match .matchDate.soon{background-color: #1778f2;padding: 4px 10px;border-radius: 5px;color: #fff;display: inline-block;font-size: 16px;}.Match .matchDate.not {background-color: #02811d;padding: 4px 10px;border-radius: 5px;color: #fff;display: inline-block;font-size: 16px;}.containerMatch.Not .natej,.containerMatch.Soon .natej{display: none;}.containerMatch.End .matchHour{display: none;}.containerMatch.Live #matchHour,.containerMatch.Live .natej{display: none;}.containerMatch a .Match .fareq img{width: 50px;height: 100%;}body.StyleSite1 h2.boxstitle {display: none!important;}body.StyleSite1 ul.nav-tabs {float: none!important;gap: 0 20px;}
.ASW ul.labels {right: -300px!important;transition: all .3s;}
.ASW .Posts .item:hover ul.labels {right: 3px!important;}
/*====== Post In Post ======*/
.Related .Posts{margin:1px auto}.Related .RelatedASW{box-shadow:0 0 0 #1010164a;border:0 solid #eee;padding:0;border-radius:0}.Related .Posts ul.labels{display:none}.Related .headline{position:relative;border-radius:10px;background:var(--BP);color:var(--CO);padding:10px;box-shadow:0 0 0 #10101614;border:0 solid #eee}body.singleItem .post-body img{border-radius:5px;width:auto;height:auto;display:inline;max-width:100%}body.singleItem .post-body .separator a{margin:0!important}.PostTop .Title{font-size:25px;text-align:center;font-weight:700;padding:10px 5px 1px;color:var(--CO);transition:.3s .1s all ease;width:100%;display:grid;justify-content:center;align-items:center}.PostTop{background:var(--BP);border-radius:10px 10px 0 0;margin:0 0 20px}body.singleItem main#main div#page_body{background: none!important;border-radius:0px 00px 0px 0px!important;padding:0;box-shadow: 0 0px 0px #10101600!important;border: 0px solid #eee;}body.singleItem main#main article.Item .PostContent {overflow: hidden;background:var(--BG2);border-radius: 10px;padding: 5px;box-shadow: 0 1px 7px #0404600f;border: 1px solid var(--BR)}.Author .left{display:none}.PostTop .Info .Author svg,.PostTop .Info .datetime svg{width:15px;color:#6c757d;float:right;margin:0 0 0 5px}.PostTop .Info>div{display:inline-block;margin:0 4px}.PostTop .Info>div .author-name {display: flex;justify-content: center;align-items: center;}.PostTop .Info>div .author-name>a {font-size: 16px;}.PostTop .Info{text-align:center;padding:10px}body.singleItem .post-body{padding:20px;line-height: 2;}body.singleItem div.edit-post.item-control a{color:#444;padding:5px 20px;border-radius:20px;font-size:14px;cursor:pointer;border:3px solidvar(--BG);font-weight:600}body.singleItem div.edit-post.item-control a:hover{color:#fff;background:var(--BG)}body.singleItem div.edit-post.item-control{display:block;text-align:center;margin:0 0 10px}body.singleItem div.labels ul.labels li a{background:var(--GD);padding:6px 8px;display:block;color:var(--CO);border-radius:5px;font-weight:600;display:inline-block}body.singleItem div.labels ul.labels li{display:inline-block;margin:2px 3px;overflow:hidden}body.singleItem div.labels ul.labels li:hover{transform:scale(1.1)}body.singleItem div.labels{padding:10px}.post-body ol li,.post-body ul li{margin-bottom:6px}.post-body ul li {list-style: disc;}.post-body b {font-weight: 700;}.post-body ul{list-style:circle inside}.post-body dl,.post-body ol,.post-body ul{list-style:none;margin-top:0;padding-right:20px}.post-body ol{list-style:decimal inside}.post-share {display: grid;justify-content: center;grid-template-columns: repeat(9,1fr);text-align: center;align-items: center;margin: 0 10px 25px 10px;gap: 10px 20px;}.post-share>div a {text-align: center;display: block;padding: 5px 10px;}.post-share>div a svg {width: 25px;display: block;margin: 0 auto;}.post-share>div {transition: all .3s;background: #dddd;border-radius: 5px;text-align: center;transform: translate(0px, 10px);}.post-share>div:hover {transform: translate(0px, 0px);}.post-share>div.facebook {background: #3059b0;color: #fff;fill: #fff;}.post-share>div.whatsapp {background: #25d366;color: #fff;fill: #fff;}.post-share>div.twitter {background: #55acef;color: #fff;fill: #fff;}.post-share>div.Email {background: #eee;}.post-share>div.Line {background: #00b300;color:#fff;fill: #fff;}.post-share>div.Tumblr {background: #385672;color: #fff;fill: #fff;}.post-share>div.Linkedin {background: #0e76a8;color: #fff;fill: #fff;}.post-share>div.beside {background: #e60023;color: #fff;fill: #fff;}.post-share>div.Telegram {background: #0088cc;color: #fff;fill: #fff;}body.singleItem blockquote {background: var(--BP);border-radius: 5px;padding: 20px;margin: 15px 0;font-size: 15px;}body.singleItem .post-body h1,body.singleItem .post-body h2,body.singleItem .post-body h3,body.singleItem .post-body h4 {margin-top: 0;margin-bottom: .5rem;line-height: 1.2;}body.singleItem .post-body h1 {font-size: 2.5rem;}body.singleItem .post-body h2 {font-size: 2rem;}body.singleItem .post-body h3 {font-size: 1.75rem;}body.singleItem .post-body h4 {font-size: 1.5rem;}.post-body div#SportGo,.post-body div#SportGoo {border-radius: 10px;overflow: hidden;margin: 15px auto;text-align: center;}.post-body div#SportGoo>div {height: 500px!important;width: 100%!important;}.post-body ol li{list-style: decimal inside;}
ul.ListPost li a:hover {color:var(--CO);}ul.ListPost li a {color: var(--CO);font-weight: 600;}ul.ListPost:before {content: 'قد يهمك';background:var(--BGS);color: #fff;padding: 2px 25px;font-size: 16px;border-radius: 14px;position: absolute;top: -19px;right: 8px;}ul.ListPost {background:var(--BG1);padding: 20px 30px 5px 30px;border-radius: 5px;margin: 30px 0 10px 0;position: relative;}div#PostsEnd .widget.HTML .widget-content {font-size: 16px;line-height: 25px;max-width: 1100px;margin: 0 auto;background: var(--BG2);border-radius: 0 0 10px 10px;padding: 10px;box-shadow: 0 1px 7px #1010164a;}
/*====== blog-pager ======*/
div#blog-pager {margin-top: 2px;text-align: center;clear: both;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}div#blog-pager span{width: 35px;height: 35px;line-height: 35px;background-color: #e1e1e1e1;border-radius: 100%;display: inline-block;text-align: center;margin: 04px;cursor: pointer;font-weight: 600;}div#blog-pager span.pageNum.current.inStyle {background:#0066cc;cursor: auto!important;color: #ffffff!important;}div#blog-pager span.pageNum a {color: #111;display: block;}span.showPageOf {display: none!important;}svg.line{fill:none;stroke:#161617;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5}div#themeDark svg{width:25px;stroke:#fff;height:25px;max-height:25px;max-width:25px;min-height:25px;min-width:25px}div#themeDark{margin: 0 10px 6px 0;display:flex;justify-content: center;align-items: center;float:left;cursor:pointer;background:var(--BG);width:35px;height:35px;textalign:center;border-radius:10px;padding:4px}

/*===== TOPfooter ====*/
.TOPfooter div#TOPfooter>div ul li:hover:before {border-radius: 50%;background:var(--CO);}.TOPfooter div#TOPfooter>div ul li a:hover {color:var(--CO)}.TOPfooter div#TOPfooter>div .headline:after {position: absolute;width: 59px;height: 6px;background:var(--BG);display: block;top: 34px;right: 0px;content: '';border-radius: 10px;}.TOPfooter div#TOPfooter>div ul {padding: 0 10px 0 0;}.TOPfooter div#TOPfooter>div .headline {border-bottom: 2px solid #222;margin: 0 0 6px 0;position: relative;}.TOPfooter {margin: 50px 0 0 0;box-shadow:0 1px 7px #1010164a;padding: 10px;background:var(--BFO);}.TOPfooter div#TOPfooter {max-width: 1100px;margin: 0 auto;gap: 10px 15px;text-align: center;display: grid;justify-content: center;grid-template-columns: 1fr 1fr 1fr 1fr;}.TOPfooter div#TOPfooter .headline {position: relative;color: var(--CO);padding: 0 0 6px 0;box-shadow: none;border: 0;text-align: right;}.TOPfooter div#TOPfooter ul li:before {content: '';background: var(--CO);width: 8px;height: 8px;display: block;position: absolute;top: 8px;right: -13px;transform: rotate(45deg);}.TOPfooter div#TOPfooter ul li a {display: inline-block;color:var(--CO);font-size: 14px;}.TOPfooter div#TOPfooter ul li {display: block;margin: 0 13px 5px 0;position: relative;text-align: right;}.TOPfooter div#TOPfooter ul {text-align: right;}.Dark .TOPfooter div#TOPfooter>div .headline{background: none;}

/*======== footer =========*/
footer{border-top: 2px solid #ddd; display: flow-root;background:var(--BG2);padding: 10px 10px;position: relative;}footer>div {max-width: 1000px;display: block;margin: 0 auto;}footer>div .copyrightSafe {float: right;}footer>div div#LinkFooter {float: left;}footer>div div#LinkFooter ul li a {color: var(--CO);font-weight: 600;}footer>div div#LinkFooter ul li {display: inline-block;margin: 0 15px 0 0;}footer>div .copyrightSafe>div {font-weight: 600;line-height: 2.2;color: var(--CO);display: flex;justify-content: center;align-items: center;gap: 0 5px;}footer>div .copyrightSafe>div a {font-weight: 700;    font-size: 16px;}

/*====== GetBtaka =====*/
.coMatch{position:relative;margin-bottom:15px;border-radius:8px;padding:12px;background:#fff;box-shadow:0 0 4px rgba(0,0,0,.3)}.backMatch{overflow:hidden;position:relative;display:block;border-radius:8px;background:url(https://1.bp.blogspot.com/-z_wIECewHDs/X5rSmEuvx1I/AAAAAAAAYus/2VQNQHtTmsg1gYcwOr7G-89KwyPSooQkACLcBGAsYHQ/s0/match.jpg);background-size:100% 100%}.baMatch{ overflow: hidden;background: rgb(0 0 0 / 8%);display: grid;align-items: center;grid-template-columns: 1fr 1fr 1fr;justify-content: center;}.colmd{text-align:center;    padding: 10px 0;color:#fff;}.teamlogo{max-width:100px;height:100px;background-size:cover;margin:15px auto}.colmd.ce{width:20%}.matchvs{display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:9;right:0;bottom:0;left:0;margin:0;top:0;content:'vs';font-size:80px;font-weight:bold;margin-top:25px;}.colmd .teamname {font-size: 20px;}

/*====== contact =====*/
#contact-form{padding:20px;}div#Pagecontactus{margin:10px 0}#ContactForm1_contact-form-name,#ContactForm1_contact-form-email,#ContactForm1_contact-form-email-message{color:var(--CO);margin:5px auto;border:1px solid#d6d6d6;transition:all .5s ease-out;width:100%;border-radius:5px;padding:8px 15px;margin-bottom:10px;background:transparent;font:400 14px 'Segoe UI'}input#ContactForm1_contact-form-name{margin-top:0}#ContactForm1_contact-form-submit{border-radius: 5px;background:#eee;cursor:pointer;font-weight:bold;padding:8px 15px;color:#3c3c3c;margin:0 0;fontsize:13px;border:1px solid #d4d4d4}div#ContactForm1_contact-form-error-message img{vertical-align:middle;margin-right:3px}textarea#ContactForm1_contact-form-email-message{margin-bottom:5px}#ContactForm1_contact-form-name:focus,#ContactForm1_contact-form-email:focus,#ContactForm1_contact-form-email-message:focus{outline:none;bordercolor:rgb(60,91,146);border-style:solid}#contact-form .bott {text-align: center;}
/* UP */
.UP span {display: block;}.UP svg {cursor: pointer;box-shadow: 0 1px 7px #ffffff4a;display: block;background:var(--BGS);fill: #ffff;padding: 3px;width: 35px;height: 35px;border-radius: 50%;}.UP {display: inline-block;position: fixed;left: 25px;bottom: 25px;    z-index: 2;}div#blog-pager a {display: inline-block;background:var(--BG);color: #fff;padding: 5px 10px;border-radius: 5px;}

/*====== Error =====*/
.Error p {font-size: 20px;}.Error {margin: 100px auto;text-align: center;background: var(--BG2);box-shadow: 0 1px 7px #1010164a;padding: 20px;border-radius: 10px;}
.Error-404 div#page_body{display:none;}
/*======> Post H <=====*/
body.singleItem .post-body h1,body.singleItem .post-body h2, body.singleItem .post-body h3,body.singleItem .post-body h4{display: table;padding: 5px 15px;background-color: var(--GD);font-size: 18px;font-weight: 600;margin: 15px 0;line-height: 1.5;text-align: right;border-radius: 8px;}body.singleItem .post-body h1 {font-size: 1.2rem;}body.singleItem .post-body h2 {font-size: 1.1rem;}body.singleItem .post-body h3 {font-size: 1rem;}body.singleItem .post-body h4 {font-size: 0.9rem;}

/*====== table =====*/
.table-bordered{width:100%;border-collapse:collapse;background-color:var(--BT);border-radius:8px;margin-bottom:15px;}.table-bordered tr{border-bottom:1px solid var(--BTR);}.table-bordered tr:last-child{border:0}.table-bordered td,table th{padding:10px;}.table-bordered td{border-right:1px solid var(--BTR);}.table-bordered th {font-weight: 700;font-size: 16px;}
.TableMatch>h3 {width: 100%;display: block;padding: 10px!important;margin: 5px 0 0 0!important;}

/*====== Thing =====*/
div.loading{-webkit-animation:shownow .5s ease-in-out;-moz-animation:shownow .5s ease-in-out;animation:shownow .5s ease-in-out;display:block;margin:0 auto;background-image:var(--Loding);background-size:40px;background-repeat:no-repeat;background-position:49% 50%}div.loading {height: 70px;}.AdsContent {background: transparent!important;margin: 15px 0;text-align: center;font-size: 13px;display: block;clear: both;border: none;overflow: unset!important;box-shadow: none;padding: 0!important;border-radius: 0;}header .Logo div#Header1 .description {display: none;}a img.lazy{background-color:var(--BL); border-radius: 7px 7px 0 0;overflow: hidden;}div#ADSTopPost {text-align: center;margin: 0 auto 10px auto;display: block;    overflow: hidden;}
.NotMatches {text-align: center;font-size: 20px;font-weight: 700;padding: 18px;}.HideRelated {display: none!important;}body div#themeDark svg.line.svg-2,body.Dark div#themeDark svg.line.svg-1 {display: none;}body.Dark div#themeDark svg.line.svg-2 {display: block;}
div#ADSTopPost .AdsContent {margin: 0px 0;}
body.multipleItems ul.labels li:not(body.multipleItems ul.labels li:first-child) {display: none;}
div#Player {font-size: 0px;}
/*================= Responsev ===================*/

@media screen and (max-width: 992px){body.multipleItems .blog-posts.hfeed.container,body.multipleItems div.Posts,.Related .Posts{grid-template-columns:repeat(3,1fr)}.option{text-align:center;display:grid;justify-content:center;align-items:center;grid-template-columns:1fr 1fr;gap:0 10px}.containerMatch a .Show{background-size:10% 50%}}@media screen and (max-width: 860px){.containerMatch a .Show{background-size:10% 50%}.post-share{grid-template-columns:repeat(5,1fr)!important}.post-body div#SportGoo > div{height:430px!important}.TOPfooter div#TOPfooter{grid-template-columns:1fr 1fr}.post-body div#SportGoo > div{height:400px!important}footer > div ul.socialAS li{margin:0 0 7px!important;display:block!important}body.multipleItems div.Posts,body.multipleItems .blog-posts.hfeed.container,.Related .Posts{grid-template-columns:repeat(2,1fr);gap:7px 50px}div#Menut{position:fixed;top:0;background:var(--BFO);height:100%;bottom:0;z-index:10;right:-1000px;padding:5px 1px;box-shadow:0 1px 7px #1010164a;width:270px}header .Menut.section.open{right:0!important}.option{display:inline-block}.containerMatch a .Show{background-size:13% 50%}.post-share{grid-template-columns:repeat(3,1fr)}.teamlogo{max-width:70px;height:70px}.matchvs{font-size:50px}.Menu div#LinkList2 ul li{display:block;margin:10px 18px}.Menu{display:flex;height:auto}header .Logo{margin:0;float:none}header > div{text-align:center}div#themeDark{position:absolute;top:20px;left:20px}.Menu div#LinkList2 ul li:first-child{display:block}.Menu div#LinkList2 ul li:first-child span svg g{stroke:#fff}.Menu div#LinkList2 ul li:first-child span svg{display:block;float:left;height:100%;cursor:pointer}.Menu div#LinkList2 ul li:first-child span{background:#222;display:inline-block;padding:10px 88px;border-radius:5px}header{padding:10px 10px 2px}ul.socialAS{justify-content:center}div#Menut div#LinkList8{float:none;margin-top:10px}header .MenuX{right:20px;top:20px;position:absolute;cursor:pointer;display:block}header .MenuX svg{height:auto;width:40px}}@media screen and (max-width: 640px){.containerMatch a ul {display: none;}body.multipleItems div.Posts,.Related .Posts,body.multipleItems .blog-posts.hfeed.container{grid-template-columns:repeat(2,1fr);gap:7px 30px}.Menu{margin:0}.containerMatch a .Show{background-size:15% 50%}.post-body div#SportGoo > div{height:350px!important}.containerMatch a .Match .fareq img{width:50px;height:100%}.Title a{font-weight:400;font-size:15px}}@media screen and (max-width: 550px){body.multipleItems div.Posts,.Related .Posts,body.multipleItems .blog-posts.hfeed.container{grid-template-columns:repeat(2,1fr);gap:7px 15px}.containerMatch a .Show{background-size:15% 40%}main#main,div#PostsEnd,div#PostsTop,.TabulaSoprt{width:94%}}@media screen and (max-width: 480px){body.multipleItems div.Posts,.Related .Posts,body.multipleItems .blog-posts.hfeed.container{grid-template-columns:repeat(2,1fr);gap:7px 10px}header .Logo{margin:0 0 0 15px}footer div#LinkFooter{float:none;display:block;text-align:center}footer div#LinkFooter div#LinkList1{display:block;margin:5px 0 0}.containerMatch a .Show{background-size:15% 40%}footer > div{text-align:center;padding:0 5px}footer > div div#LinkFooter,footer > div .copyrightSafe{float:none}footer > div .copyrightSafe{margin:10px 0 0}.containerMatch a ul li:nth-child(2){display:none}.containerMatch a ul{grid-template-columns:repeat(2,1fr)}.post-body div#SportGoo > div{height:300px!important}.TOPfooter div#TOPfooter{grid-template-columns:1fr}.TabulaSoprt .TabulaGo h2.boxstitle,.TabulaSoprt .TabulaGo ul.nav-tabs{float:none}.TabulaSoprt .TabulaGo ul.nav-tabs{margin:5px 0 0}}@media screen and (max-width: 470px){body.multipleItems div.Posts,.Related .Posts,body.multipleItems .blog-posts.hfeed.container{grid-template-columns:repeat(1,1fr);gap:7px 10px}}@media screen and (max-width: 400px){div#Menut{width:230px;padding:10px}.asm{font-size:14px;font-weight:600;margin:3px 0 0}.containerMatch a .Match .fareq .Imagee img{width:50px;height:50px}.containerMatch a .Show{background-size:21% 40%}.post-body div#SportGoo > div{height:200px!important}.containerMatch a ul li{font-size:14px}.Menu div#LinkList2 ul li a{padding:2px 10px}.Menu div#LinkList2 ul li{margin:10px auto}}@media screen and (max-width: 340px){.containerMatch a .Show{background-size:20% 36%}.post-share{grid-template-columns:repeat(2,1fr)}.post-body div#SportGoo > div{height:170px!important}header .Logo div#Header1{width:160px}}@media screen and (max-width: 320px){.closeSearch{left:25%}.containerMatch a .Show{background-size:31% 45%}.post-body div#SportGoo > div{height:200px!important}}
#Player-Dplayer > div.fp-player > a{opacity:0!important}body.Player,body.Player .PostContent{background:#111!important;box-shadow:0 0 0 #0404600f!important;border:0!important}body.Player .Post-Player > div,body.Player video#Player-Hlsjs{direction:ltr;width:100%!important;height:100%!important;position:fixed;left:0;right:0;top:0;bottom:0}button#bmpui-id-212{display:none}.video-serv{margin-top:15px;overflow:hidden;float:right;width:100%;margin-bottom:5px;clear:both;display:flex;justify-content:space-between}.video-serv a{float:right;background:#931800;border-radius:4px;text-shadow:0 1px 1px #000;color:#fff!important;font-weight:700;padding:5px 15px;font-size:16px;flex:1;text-align:center;margin:3px}

]]></b:skin>
<style>
  <b:if cond='data:skin.vars.HideComment != &quot;2px&quot;'>#comments{padding:10px;overflow:hidden;background:#fff;border-radius:5px;-webkit-box-shadow:0 2px 4px rgba(43,59,93,0.13);-moz-box-shadow:0 2px 4px rgba(43,59,93,0.13);box-shadow:0 2px 4px rgba(43,59,93,0.13);margin-top:8px}.comments-bar{display:block;overflow:hidden}.comments-bar li{font-weight:700;float:right;padding:10px 15px;margin-left:10px;border-radius:2px 2px 1px 1px;background-color:#eee;cursor:pointer;-webkit-transform:translate(0,7px);-ms-transform:translate(0,7px);font-size:11px}.comments-bar .active{-webkit-transform:translate(0);-ms-transform:translate(0);color:#fff}li[data-tabs=&quot;facebook-tab&quot;]:hover,li[data-tabs=&quot;facebook-tab&quot;]{background-color:#1778f2;color:#fff}li[data-tabs=&quot;disqus-tab&quot;]:hover,li[data-tabs=&quot;disqus-tab&quot;]{background-color:#2e9fff;color:#fff}li[data-tabs=&quot;blogger-tab&quot;]:hover,li[data-tabs=&quot;blogger-tab&quot;]{background-color:#f87850;color:#fff}.comments-tabs{clear:both}.comments-tabs &gt; div{display:none;padding:15px 0;text-align:center;border-width:4px 0;border-style:solid}.comments-tabs .active{display:block}.comments-tabs .facebook-tab{border-color:#1778f2}.comments-tabs .disqus-tab{border-color:#2e9fff}.comments-tabs .blogger-tab{border-color:#f87850;text-align:right}#comments .comments-content{padding:15px 0}#comments .comments-content ol li{list-style:none;position:relative;display:block;margin:20px 0 0 8px}#comments .comments-content ol li .avatar-image-container{display:block;width:40px;height:40px;overflow:hidden;border-radius:50px;margin-bottom:5px}#comments .comments-content ol li .avatar-image-container img{width:100%;height:100%}#comments .comments-content ol li .comment-block{background:#f1f1f1;padding:21px 24px;-webkit-box-shadow:0 2px 4px rgba(43,59,93,0.13);-moz-box-shadow:0 2px 4px rgba(43,59,93,0.13);box-shadow:0 2px 4px rgba(43,59,93,0.13);border-radius:10px}#comments .comments-content ol li .comment-block .comment-header{font-size:15px}#comments .comments-content ol li .comment-block .comment-header cite.user{position:absolute;top:10px;right:50px;font-style:normal}#comments .comments-content ol li .comment-block .comment-header .datetime.secondary-text{position:absolute;top:2px;font-size:12px;background:#f1f1f1;padding:10px;color:#494949;left:0;border-radius:5px;transition:all .4s ease-out}#comments .comments-content ol li .comment-block p.comment-content{padding:10px;background:#fff;font-size:14px;border-radius:5px;color:rgba(0,0,0,0.5);display:block;overflow:hidden;margin-bottom:20px}#comments .comments-content ol li .comment-block .comment-actions a,{padding:2px 9px;margin-left:7px;background:#fff;color:#494949;font-size:14px;transition:all .4s ease-out;border-radius:5px;display:inline-block}#comments .comments-content ol li .comment-replies{padding-right:50px;margin-top:5px}#comments .comments-content ol li .comment-replies ol{display:none}#comments .comments-content ol li .comment-replies ol.thread-expanded{display:block}#comments .thread-toggle{position:relative;display:block;margin:15px 0}#comments .thread-toggle a{display:inline-block;position:relative;z-index:1;font-size:14px;background:#dddd;padding:3px 20px;border-radius:5px;color:#111}#comments .thread-toggle.thread-collapsed .thread-arrow:after{background-image:url(&quot;data:image/svg+xml,%3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; viewBox=&#39;0 0 20 20&#39; fill=&#39;%23000000bf&#39;%3E%3Cpath fill-rule=&#39;evenodd&#39; d=&#39;M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z&#39; clip-rule=&#39;evenodd&#39; /%3E%3C/svg%3E&quot;)}#comments .comment .comment-replies .comment-thread .continue,#comments .comment .comment-replies .comment-thread.hidden,div#top-continue,#comments#top-continue.continue.hidden,#comments .loadmore{display:none}#comment-editor{margin-bottom:20px;margin-top:20px}#comments .comment-form h4{position:absolute;clip:rect(1px,1px,1px,1px);padding:0;border:0;height:1px;width:1px;overflow:hidden}#comments a{color:#070a2d}#comments .commentsTitle:after{content:&#39;&#39;;background:#e8e8e8;height:10px;position:absolute;width:100%;border-radius:10px}#comments .commentsTitle h3{font-size:18px;display:inline-block;position:relative;z-index:2;background:#ddd;padding:7px 17px;border-radius:30px}#comments .commentsTitle{display:grid;justify-content:center;align-items:center;text-align:center;position:relative}span.comment-actions.secondary-text a {background: #cecece;color: #202442!important;margin: 0 6px;padding: 2px 11px;border-radius: 5px;font-weight: 600;}
</b:if>
 <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>body.ltr .containerMatch a ul li span {margin: 0 5px -6px 0;}body.ltr .TabulaSoprt .TabulaGo ul.nav-tabs {float: right;}body.ltr div#themeDark{margin: -6px 0px 0 10px!important;}body.ltr .headline a.Lapel-Link ,body.ltr footer&gt;div div#LinkFooter{direction: ltr;float: right;}body.ltr .TabulaSoprt .TabulaGo h2.boxstitle,body.ltr footer&gt;div .copyrightSafe,body.ltr header .Logo{direction: ltr;float: left;}@media screen and (max-width: 720px){body.ltr div#themeDark{float: right;right: 20px;left: auto;; margin: -8px 0px 0 10px;}body.ltr div#themeDark{margin:0px 0px 0 10px!important;}body.ltr header .Logo{float: none;}}body.ltr .TabulaSoprt .TabulaGo h2.boxstitle:before {margin-right: 10px;}body.ltr .copyright a.Anubis-Web {margin: 0 5px 0 0!important;float: left!important;}body.ltr .TOPfooter div#TOPfooter .headline,.TOPfooter div#TOPfooter ul{text-align: left;}body.ltr .TOPfooter div#TOPfooter&gt;div .headline:after{left:0px;}body.ltr .TOPfooter div#TOPfooter ul li:before { left: -17px;}body.ltr .TOPfooter div#TOPfooter ul li { margin: 0px 0px 5px 20px;}body.ltr div#Menut.open {left: 0;}body.ltr div#Menut{left: -1000px;}body.ltr #comments .comments-content ol li .comment-block .comment-header .datetime.secondary-text{      right: 0;  left: auto;}body.ltr #comments .comments-content ol li .comment-block .comment-header cite.user {left: 50px;right: auto;}body.ltr #comments .comments-content ol li .comment-replies {padding-left: 50px;padding-right: 0px;}body.ltr div#Menut div#LinkList8 {float: right;}body.ltr .PostTop .Info .Author svg, body.ltr .PostTop .Info .datetime svg{margin: 0 5px 0 5px;}body.ltr header .MenuX {right: auto;left: 20px;top: 20px;position: absolute;cursor: pointer;}</b:if>
  </style>
    </b:if>
<b:if cond='data:view.isLayoutMode'>
<b:template-skin>/*<![CDATA[*/

body#layout .rtl{direction:rtl}
body#layout .ltr{direction:ltr}
body#layout div.section{background-color:#fff;border:1px solid #ededed;margin:0 4px 8px;overflow:hidden;padding:16px;position:relative;border-radius:10px;text-align:center;font-family:Tahoma ,"Segoe UI"!important}
body#layout .Blog .widget-content{height:40px}
body#layout .Social_Page .widget.locked-widget,body#layout div#Logo_Ads .widget.HTML.locked-widget{width:45%;display:inline-block;float:left}
body#layout .Social_Page .widget.PageList.locked-widget,body#layout div#Logo_Ads .widget.Header.locked-widget{float:right;width:45%}
body#layout .section h4{font-size:15px;line-height:24px;margin:2px 0 8px;text-align:center;display:inline-block;background:#3560ab;color:#fff;border-radius:5px;padding:2px 9px}
body#layout .widget.draggable-widget .widget-content{border-radius:4px!important}
body#layout{max-width: 90%;min-width: 90%;height: auto;border: 2px solid #eee;font-size: small;margin: 20px auto;padding: 210px 5px 10px 5px;text-align: center;border-radius: 10px;font-family: Segoe UI,sans-serif;background-color: #efefef;background:#efefef url(https://3.bp.blogspot.com/-7be7oo1TgG4/WmSZXInnR5I/AAAAAAAAACc/z04mSX3fHpIblUl88DdieGnJXce4oIlwwCLcBGAs/s1600/pattern.png);direction: rtl;position: relative;}body#layout:before{content:'';margin-bottom:10px;background:url(https://1.bp.blogspot.com/-8WsU3Y9EEs0/YAgN8g5f_VI/AAAAAAAAALc/dAWbXdxFHDIdRrUisaslPiTvkZTraElPACLcBGAsYHQ/s16000/logotemp.png) no-repeat center center;display: block;height: 200px;position: absolute;width: 100%;top: 0;left: 0;right: 0;border-radius: 5px 5px 0px 0px;background-size: 100% 100%;}body#layout .clear{height:1px;clear:both;display:block;width:100%}
body#layout .section .widget-content{background:#0b0e2f;border-radius:5px;min-height:50px}
body#layout .draggable-widget .widget-wrap3{background:none}
body#layout .section h4{display:none}
body#layout div.layout-title{color:#ddd;font-weight:700;text-align:right;font-size:13px}
body#layout div.layout-widget-description{color:#b77881;font-size:11px;text-align:right;line-height:16px}
body#layout div.layout-title,body#layout div.layout-widget-description{font-family:tahoma}
body#layout .main-container .section{margin:0;border:0;padding:0;background:0;font-size:0;height:auto}
body#layout div.section>div.add_widget{margin-top:0;padding:7px 15px;border:none;border-bottom:2px solid #c7c7c7}
body#layout div.section>div.add_widget:hover{border-bottom:2px solid #e87375}
body#layout .add-icon{float:right;background-color:#d8d8d8;border-radius:2px}
body#layout div.section>div.add_widget:hover .add-icon{background-color:#e87375}
body#layout div.section>div.widget{margin-top:0;margin-bottom:10px}
body#layout .section .widget a.editlink{border:0;padding:3px 15px;color:#d4d9e4!important;left:10px;right:auto;background:#384C80;text-decoration:none;border-radius:3px;height:20px;font:700 11px/18px Tahoma}
body#layout div#Setting:before {content: 'الاعدادات';margin: 0 0 10px 0;display: inline-block;background: #070a2d;padding: 1px 16px;border-radius: 30px;color: #fff;font:700 18px/27px Tahoma}
body#layout .section .widget a.editlink:hover{background:#ef770f;color:#fff!important}
body#layout .visibility .editlink.icon{margin-top:15px}
body#layout .add_widget{border:1px dashed rgba(0,0,0,0.3);margin-bottom:5px;margin-top:0}
body#layout .add_widget:hover{border:1px dashed rgba(0,0,0,0.5)}
body#layout .section .add_widget a{text-align:right;margin-right:40px;margin-left:0;color:#757575;font-weight:700;text-decoration:none!important}
body#layout div.widget-content{padding:10px 15px}
body#layout .draggable-widget div.widget-wrap2{background-color:#ef770f}
body#layout .dr_active:before{content:"\افلت هُنا";font-size:30px;padding-top:25px;display:block;font-weight:700}
body#layout .dr_active{height:50px!important;background-color:transparent;border:1px dashed #5558ea;color:#5e1056;margin-bottom:30px;top:20px;border-radius:100px}
body#layout .widget.locked-widget:before{content:"";position:absolute;left:10px;z-index:2;top:0;background-color:#ff3300;width:17px;height:13px;color:#b190b6;line-height:1em;padding-top:5px;border-radius:0 0 20px 20px}
body#layout .TOPfooter div.widget {float: right;width: 24%;margin: 0px 4px;}
body#layout .widget .visibility .layout-widget-state{float:right;margin-top:12px;background-image:url(https://4.bp.blogspot.com/-4ewGLNY2bfg/WmSZTIyIIfI/AAAAAAAAABg/hkOX-BjuVVUjfRomeZxjQtyVzTSEKa_WgCLcBGAs/s1600/eyes.png);opacity:1!important}
.layout-widget-state.visible{background-position:center -1px!important}
.layout-widget-state.not-visible{background-position:center -23px!important}
body#layout div#ADS>div.widget {width: 48%;float: left;margin: 5px 8px;}
body#layout header>div>div {width: 50%;float: right;}
body#layout div#tabulaSoprt>div.widget {width: 32%;float: left;margin: 2px 5px;}
body#layout header:before,body#layout div#ADS:before,body#layout .TOPfooter:before,body#layout footer:before,body#layout div#PostsEnd:before,body#layout div#PostsTop:before,body#layout div#tabulaSoprt:before {content: '';margin: 0 0 10px 0;display: inline-block;background: #070a2d;padding: 1px 16px;border-radius: 30px;color: #fff;font: 700 18px/27px Tahoma;}
body#layout header:before {content: 'رأس  الموقع';}
body#layout div#ADS:before {content: 'الاعلانات';}
body#layout .TOPfooter:before {content: 'فوق  الذيل';}
body#layout footer:before {content: 'الذيل';}
body#layout div#PostsEnd:before,body#layout div#PostsTop:before {content: 'منطقة  تدوينات  اضافية';}
body#layout div#tabulaSoprt:before{content: 'جدول  المباريات';}


/*]]>*/</b:template-skin>
</b:if>
<b:if cond='data:skin.vars.BlogFonts'>
<b:if cond='data:skin.vars.BlogFonts == &quot;1px&quot;'>
<style>/*<![CDATA[*/body *{font-family:'Changa',Segoe UI,sans-serif;}/*]]>*/</style>
  	</b:if>
<b:if cond='data:skin.vars.BlogFonts == &quot;2px&quot;'>
<link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l8KiHrRpiYlJ.woff2' rel='preload'/>
<link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l8KiHrFpiQ.woff2' rel='preload'/>
<link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l4qkHrRpiYlJ.woff2' rel='preload'/>
<link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l4qkHrFpiQ.woff2' rel='preload'/>
<style>/*<![CDATA[*/
/* arabic */
@font-face{font-family:'Tajawal';font-style:normal;font-weight:500;font-display:swap;src:local('Tajawal Medium'),local('Tajawal-Medium'),url(https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l8KiHrRpiYlJ.woff2) format('woff2');unicode-range:U+0600-06FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE80-FEFC}
/* latin */
@font-face{font-family:'Tajawal';font-style:normal;font-weight:500;font-display:swap;src:local('Tajawal Medium'),local('Tajawal-Medium'),url(https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l8KiHrFpiQ.woff2) format('woff2');unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}
/* arabic */
@font-face{font-family:'Tajawal';font-style:normal;font-weight:700;font-display:swap;src:local('Tajawal Bold'),local('Tajawal-Bold'),url(https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l4qkHrRpiYlJ.woff2) format('woff2');unicode-range:U+0600-06FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE80-FEFC}
/* latin */
@font-face{font-family:'Tajawal';font-style:normal;font-weight:700;font-display:swap;src:local('Tajawal Bold'),local('Tajawal-Bold'),url(https://fonts.gstatic.com/s/tajawal/v3/Iurf6YBj_oCad4k1l4qkHrFpiQ.woff2) format('woff2');unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}
body *:not(.fa){font-family:'Tajawal',sans-serif}

/*]]>*/</style>
  	</b:if>
<b:if cond='data:skin.vars.BlogFonts == &quot;3px&quot;'>
      <link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/cairo/v10/SLXGc1nY6HkvalIkTpu0xg.woff2' rel='preload'/>
<link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/cairo/v10/SLXGc1nY6HkvalIvTpu0xg.woff2' rel='preload'/>
<link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/cairo/v10/SLXGc1nY6HkvalIhTps.woff2' rel='preload'/>
      <style>/*<![CDATA[*/
/* arabic */
@font-face{font-family:'Cairo';font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/cairo/v10/SLXGc1nY6HkvalIkTpu0xg.woff2) format('woff2');unicode-range:U+0600-06FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE80-FEFC}
/* latin-ext */
@font-face{font-family:'Cairo';font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/cairo/v10/SLXGc1nY6HkvalIvTpu0xg.woff2) format('woff2');unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}
/* latin */
@font-face{font-family:'Cairo';font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/cairo/v10/SLXGc1nY6HkvalIhTps.woff2) format('woff2');unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}

body *:not(.fa){font-family:'Cairo',sans-serif}

/*]]>*/</style>
  	</b:if>

	
  
</b:if>
<b:defaultmarkups>
    <b:defaultmarkup type='Common'>
      <b:includable id='contactUsJs'> 
<b:if cond='data:view.isPage'>
<script src='https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js'/>
<b:tag name='script' type='text/javascript'>
  
  window.addEventListener(&#39;load&#39;, ()=&gt; {
 
  
if($(&quot;.PageContactus&quot;).length){
$.getScript(&quot;https://www.blogger.com/static/v1/widgets/2227587253-widgets.js&quot;)
.done(function() {
<b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>
$htmlcontact = &quot;<div id='contact-form'><form name='contact-form'><input class='contact-form-name' id='ContactForm1_contact-form-name' name='name' placeholder='name *' size='30' type='text' value=''/><input class='contact-form-email' id='ContactForm1_contact-form-email' name='email' placeholder='e-mail *' size='30' type='text' value=''/><textarea class='contact-form-email-message' cols='25' id='ContactForm1_contact-form-email-message' name='email-message' placeholder='message *' rows='5'/><input id='ContactForm1_contact-form-submit' type='button' value='Send your message'/>  <div style='text-align: center;'><div id='ContactForm1_contact-form-error-message'/><div id='ContactForm1_contact-form-success-message'/></div></form></div>&quot;;
$(&#39;.PageContactus&#39;).append($htmlcontact)
_WidgetManager._Init(&#39;&#39;);_WidgetManager._RegisterWidget(&#39;_ContactFormView&#39;, new _WidgetInfo(&#39;ContactForm1&#39;, &#39;contact-sec&#39;, null, document.getElementById(&#39;ContactForm1&#39;), {&#39;contactFormMessageSendingMsg&#39;: &#39;Sending ...&#39;, &#39;contactFormMessageSentMsg&#39;: &#39;Your message has been sent.&#39;, &#39;contactFormMessageNotSentMsg&#39;: &#39;The message could not be sent , please try again at a later time.&#39;, &#39;contactFormInvalidEmailMsg&#39;: &#39;A valid email address is required.&#39;, &#39;contactFormEmptyMessageMsg&#39;: &#39;Message field cannot be empty&#1611;ا.&#39;, &#39;title&#39;: &#39;Contact form&#39;, &#39;blogId&#39;: &#39;<data:blog.blogId/>&#39;, &#39;contactFormNameMsg&#39;: &#39;name&#39;, &#39;contactFormEmailMsg&#39;: &#39;e-mail&#39;, &#39;contactFormMessageMsg&#39;: &#39;Message&#39;, &#39;contactFormSendMsg&#39;: &#39;send&#39;, &#39;submitUrl&#39;: &#39;https://www.blogger.com/contact-form.do&#39;}, &#39;displayModeFull&#39;));})}
<b:else/>
$htmlcontact = &quot;<div id='contact-form'><form name='contact-form'><input class='contact-form-name' id='ContactForm1_contact-form-name' name='name' placeholder='الاسم *' size='30' type='text' value=''/><input class='contact-form-email' id='ContactForm1_contact-form-email' name='email' placeholder='بريد إلكتروني *' size='30' type='text' value=''/><textarea class='contact-form-email-message' cols='25' id='ContactForm1_contact-form-email-message' name='email-message' placeholder='الرسالة *' rows='5'/><div class='bott'><input id='ContactForm1_contact-form-submit' type='button' value='إرسال رسالتك'/> </div> <div style='text-align: center;'><div id='ContactForm1_contact-form-error-message'/><div id='ContactForm1_contact-form-success-message'/></div></form></div>&quot;;
$(&#39;.PageContactus&#39;).append($htmlcontact)
_WidgetManager._Init(&#39;&#39;);_WidgetManager._RegisterWidget(&#39;_ContactFormView&#39;, new _WidgetInfo(&#39;ContactForm1&#39;, &#39;contact-sec&#39;, null, document.getElementById(&#39;ContactForm1&#39;), {&#39;contactFormMessageSendingMsg&#39;: &#39;جار&#1613; الإرسال...&#39;, &#39;contactFormMessageSentMsg&#39;: &#39;تم إرسال رسالتك.&#39;, &#39;contactFormMessageNotSentMsg&#39;: &#39;تعذر إرسال الرسالة&#1548; يرجى المحاولة مرة أخرى في وقت لاحق.&#39;, &#39;contactFormInvalidEmailMsg&#39;: &#39;يلزم إدخال عنوان بريد إلكتروني صالح.&#39;, &#39;contactFormEmptyMessageMsg&#39;: &#39;لا يمكن أن يكون حقل الرسالة فارغ&#1611;ا.&#39;, &#39;title&#39;: &#39;نموذج الاتصال&#39;, &#39;blogId&#39;: &#39;<data:blog.blogId/>&#39;, &#39;contactFormNameMsg&#39;: &#39;الاسم&#39;, &#39;contactFormEmailMsg&#39;: &#39;بريد إلكتروني&#39;, &#39;contactFormMessageMsg&#39;: &#39;رسالة&#39;, &#39;contactFormSendMsg&#39;: &#39;إرسال&#39;, &#39;submitUrl&#39;: &#39;https://www.blogger.com/contact-form.do&#39;}, &#39;displayModeFull&#39;));})}
</b:if>
  
  });
</b:tag>
</b:if>
</b:includable>
    <b:includable id='DefaultMeta'>
      <meta content='text/html; charset=UTF-8' http-equiv='Content-Type'/>
<meta content='width=device-width, initial-scale=1.0, shrink-to-fit=no' name='viewport'/>
      <link expr:href='data:view.url.canonical' rel='canonical'/>
      <meta expr:content='data:view.description.escaped' name='description'/>
      <link expr:href='data:blog.blogspotFaviconUrl' rel='icon' type='image/x-icon'/>
      <meta content='IE=edge' http-equiv='X-UA-Compatible'/>
      <meta content='blogger' name='generator'/>
      <meta expr:content='data:skin.vars.body_background_color' name='theme-color'/>
      <meta expr:content='data:skin.vars.body_background_color' name='msapplication-navbutton-color'/>
      <meta expr:content='data:blog.blogId' name='BlogId'/>
      <b:eval expr='data:blog.openIdOpTag'/>
      <b:if cond='data:view.featuredImage'>
        <link expr:href='data:view.featuredImage' rel='image_src'/>
        <b:else/>&lt;link href=&#39;<b:include name='altImage'/>&#39; rel=&#39;image_src&#39;/&gt;
      </b:if>
    </b:includable>
    <b:includable id='OpenGraph'>
      <meta expr:content='data:blog.localeUnderscoreDelimited == &quot;ar&quot; ? &quot;ar_AR&quot; : data:blog.localeUnderscoreDelimited' property='og:locale'/>
      <meta expr:content='data:view.url.canonical' property='og:url'/>
      <meta expr:content='data:view.title.escaped' property='og:title'/>
      <meta expr:content='data:blog.title.escaped' property='og:site_name'/>
      <meta expr:content='data:view.description.escaped' property='og:description'/>
      <meta expr:content='data:view.title.escaped' property='og:image:alt'/>
      <b:if cond='data:view.description.escaped contains &quot;Player-&quot;'>
        <meta content='no-referrer' name='referrer'/><meta content='noindex' name='robots'/></b:if>     
      <b:if cond='data:view.isMultipleItems'><meta content='website' property='og:type'/>
        <b:elseif cond='data:view.isSingleItem'/><meta content='article' property='og:type'/>
      </b:if>
      <b:if cond='data:view.featuredImage'>
        <meta expr:content='resizeImage(data:view.featuredImage, 1200, &quot;1200:630&quot;)' property='og:image'/>
        <b:else/>&lt;meta content=&#39;<b:include name='altImage'/>&#39; property=&#39;og:image&#39;/&gt;</b:if>
    </b:includable>
    <b:includable id='TwitterCard'>
      <meta content='summary_large_image' name='twitter:card'/>
      <meta expr:content='data:blog.homepageUrl' name='twitter:domain'/>
      <meta expr:content='data:view.description.escaped' name='twitter:description'/>
      <meta expr:content='data:view.title.escaped' name='twitter:title'/>
      <b:if cond='data:view.featuredImage'>
        <meta expr:content='resizeImage(data:view.featuredImage, 1200, &quot;1200:630&quot;)' name='twitter:image'/>
        <b:else/>&lt;meta content=&#39;<b:include name='altImage'/>&#39; name=&#39;twitter:image&#39;/&gt;</b:if>
      </b:includable>
    <b:includable id='DNSPrefetech'>
      <link href='https://www.blogger.com' rel='dns-prefetch'/><link href='https://script.google.com' rel='dns-prefetch'/><link href='https://fonts.gstatic.com' rel='dns-prefetch'/><link href='https://fonts.googleapis.com' rel='dns-prefetch'/><link href='https://1.bp.blogspot.com' rel='dns-prefetch'/><link href='https://2.bp.blogspot.com' rel='dns-prefetch'/><link href='https://3.bp.blogspot.com' rel='dns-prefetch'/><link href='https://4.bp.blogspot.com' rel='dns-prefetch'/><link href='https://pagead2.googlesyndication.com' rel='dns-prefetch'/><link href='https://accounts.google.com' rel='dns-prefetch'/><link href='https://resources.blogblog.com' rel='dns-prefetch'/><link href='https://www.google.com' rel='dns-prefetch'/><link href='https://connect.facebook.net' rel='dns-prefetch'/><link href='https://www.facebook.com' rel='dns-prefetch'/>
    </b:includable>
    <b:includable id='PostShare'>
 <div class='post-share' expr:data-share='data:messages.share'>
                    <div class='post-shareIcon facebook'>
                      <a aria-label='Share button' expr:href='&quot;https://www.facebook.com/sharer.php?u=&quot; + data:blog.url.canonical' rel='nofollow noreferrer' role='button' target='_blank'>
                        <svg viewBox='0 0 32 32'><path d='M24,3H8A5,5,0,0,0,3,8V24a5,5,0,0,0,5,5h8a1,1,0,0,0,1-1V20a1,1,0,0,0-1-1H15V17h1a1,1,0,0,0,1-1V12.5A2.5,2.5,0,0,1,19.5,10H22v2H21a2,2,0,0,0-2,2v2a1,1,0,0,0,1,1h1.72l-.5,2H20a1,1,0,0,0-1,1v4a1,1,0,0,0,2,0V21h1a1,1,0,0,0,1-.76l1-4a1,1,0,0,0-.18-.86A1,1,0,0,0,23,15H21V14h2a1,1,0,0,0,1-1V9a1,1,0,0,0-1-1H19.5A4.51,4.51,0,0,0,15,12.5V15H14a1,1,0,0,0-1,1v4a1,1,0,0,0,1,1h1v6H8a3,3,0,0,1-3-3V8A3,3,0,0,1,8,5H24a3,3,0,0,1,3,3V24a3,3,0,0,1-3,3H20a1,1,0,0,0,0,2h4a5,5,0,0,0,5-5V8A5,5,0,0,0,24,3Z'/></svg>
                      </a>
                    </div>
                    <div class='post-shareIcon whatsapp'>
                      <a aria-label='Share button' expr:href='&quot;https://api.whatsapp.com/send?text=&quot; + data:blog.url.canonical' rel='nofollow noreferrer' role='button' target='_blank'>
                        <svg viewBox='0 0 32 32'><path d='M16,3A13,13,0,0,0,4.53,22.13L3,27.74a1,1,0,0,0,.27,1A1,1,0,0,0,4,29a.84.84,0,0,0,.27,0l5.91-1.65a1,1,0,0,0-.53-1.93L5.42,26.56l1.15-4.3a1,1,0,0,0-.1-.76A11,11,0,1,1,16,27a11.23,11.23,0,0,1-1.84-.15,1,1,0,0,0-1.15.82,1,1,0,0,0,.82,1.15A13,13,0,1,0,16,3Z'/><path d='M15,11.21l-1.16-1.6a2.06,2.06,0,0,0-1.5-.84,2.08,2.08,0,0,0-1.62.6l-1.2,1.2a2.81,2.81,0,0,0-.8,2.08c0,1.77,1.36,4,4,6.6,3.09,3,5.23,4,6.69,4a2.7,2.7,0,0,0,2-.81l1.2-1.2a2,2,0,0,0-.24-3.11L20.8,17a2.09,2.09,0,0,0-1.83-.3l-1.49.47a.53.53,0,0,1-.26-.09,11.42,11.42,0,0,1-2.35-2.26.31.31,0,0,1,0-.11c.13-.44.35-1.15.5-1.64A2,2,0,0,0,15,11.21Zm1.29,7.63a2.33,2.33,0,0,0,1.75.2l1.54-.46,1.61,1.25L20,21c-.48.47-2.25.33-5.86-3.21-3-2.91-3.41-4.5-3.41-5.18A.89.89,0,0,1,11,12l1.28-1.19,1.18,1.65c-.16.49-.39,1.22-.51,1.65A2.12,2.12,0,0,0,13,15.51,11.24,11.24,0,0,0,16.33,18.84Z'/></svg>
                      </a>
                    </div>
                    <div class='post-shareIcon twitter'>
                      <a aria-label='Share button' expr:href='&quot;https://twitter.com/share?url=&quot; + data:blog.url.canonical' rel='nofollow noreferrer' role='button' target='_blank'>
                        <svg viewBox='0 0 32 32'><path d='M28.77,8.11a.87.87,0,0,0-.23-.2A4.69,4.69,0,0,0,29,6.54a1,1,0,0,0-.44-1,1,1,0,0,0-1.1,0,6.42,6.42,0,0,1-2.28.92,6.21,6.21,0,0,0-7.08-1A6.07,6.07,0,0,0,15,12.2a1,1,0,0,0,2-.4A4.08,4.08,0,0,1,19,7.28a4.24,4.24,0,0,1,5.12,1,1,1,0,0,0,.88.28l.25,0a1,1,0,0,0,.34,1.62,1,1,0,0,0-.36.88,13.07,13.07,0,0,1-4.89,11.24A12.75,12.75,0,0,1,7.69,24.61a9.06,9.06,0,0,0,4.54-2.18,1,1,0,0,0,.15-1.09,1,1,0,0,0-.93-.57,4,4,0,0,1-3-1.39,3.63,3.63,0,0,0,1-.35A1,1,0,0,0,10,18a1,1,0,0,0-.76-.84,4.42,4.42,0,0,1-3-2.48c.24,0,.48.05.74.06a1,1,0,0,0,1-.62A1,1,0,0,0,7.67,13C6,11.48,5.59,9.85,5.83,8.7a13.88,13.88,0,0,0,7,4,1,1,0,1,0,.38-2A12.1,12.1,0,0,1,6.39,6.31a1,1,0,0,0-.75-.38,1,1,0,0,0-.78.33,5.34,5.34,0,0,0-.31,6l-.09,0a1,1,0,0,0-.52.81,5.84,5.84,0,0,0,1.95,4.47,1,1,0,0,0-.18,1,6.63,6.63,0,0,0,3.18,3.57A13.89,13.89,0,0,1,4,23a1,1,0,0,0-.5,1.86A16.84,16.84,0,0,0,12,27.35a15.16,15.16,0,0,0,9.6-3.57,15.12,15.12,0,0,0,5.69-12.42,4.62,4.62,0,0,0,1.62-2.25A1,1,0,0,0,28.77,8.11Z'/></svg>
                      </a>
                    </div>
                    <div class='post-shareIcon Email'>
                      <a data-text='Email' expr:href='&quot;mailto:?subject=&quot; + data:post.title + &quot;&amp;body=&quot; + data:blog.url.canonical' target='_blank'>
                        <svg class='line' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><g><rect height='16' rx='5' stroke-width='1.5' width='20' x='2' y='4'/><path d='M90.15056,261.91842l1.082.83317a6.66376,6.66376,0,0,0,8.13139,0l1.02841-.79194' stroke-width='1.5' transform='translate(-83.27144 -252.82349)'/></g></svg>
                      </a>
                    </div>
                    <div class='post-shareIcon Line'>
                      <a data-text='Line' expr:href='&quot;https://timeline.line.me/social-plugin/share?url=&quot; + data:blog.url.canonical' rel='nofollow noreferrer' target='_blank'>
                        <svg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'><g><path d='M16,2C8.28,2,2,7.38,2,14c0,5.48,4.34,10.24,10.44,11.6L12,28.87a1,1,0,0,0,.37.91A1,1,0,0,0,13,30a1,1,0,0,0,.35-.06C14,29.68,30,23.58,30,14,30,7.38,23.72,2,16,2ZM14.22,27.4l.33-2.47a1,1,0,0,0-.83-1.12C8.09,22.91,4,18.78,4,14,4,8.49,9.38,4,16,4S28,8.49,28,14C28,20.61,18.14,25.66,14.22,27.4Z'/><path d='M10,15.25H8.75V12a.75.75,0,0,0-1.5,0v4a.76.76,0,0,0,.75.75h2a.75.75,0,0,0,0-1.5Z'/><path d='M24,12.75a.75.75,0,0,0,0-1.5H22a.76.76,0,0,0-.75.75v4a.76.76,0,0,0,.75.75h2a.75.75,0,0,0,0-1.5H22.75v-.5H24a.75.75,0,0,0,0-1.5H22.75v-.5Z'/><path d='M13,11.25a.76.76,0,0,0-.75.75v4a.75.75,0,0,0,1.5,0V12A.76.76,0,0,0,13,11.25Z'/><path d='M19,11.25a.76.76,0,0,0-.75.75v1.75l-1.65-2.2a.75.75,0,0,0-1.35.45v4a.75.75,0,0,0,1.5,0V14.25l1.65,2.2a.75.75,0,0,0,.6.3.67.67,0,0,0,.24,0,.75.75,0,0,0,.51-.71V12A.76.76,0,0,0,19,11.25Z'/></g></svg>
                      </a>
                    </div>                    
                    <div class='post-shareIcon Tumblr'>
                      <a data-text='Tumblr' expr:href='&quot;https://www.tumblr.com/share/link?url=&quot; + data:blog.url.canonical' rel='nofollow noreferrer' target='_blank'>
                        <svg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'><g><path d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z'/><path d='M20,19a1,1,0,0,0-1,1,1,1,0,0,1-1,1H17a1,1,0,0,1-1-1V15h3a1,1,0,0,0,0-2H16V10a1,1,0,0,0-2,0v3H12a1,1,0,0,0,0,2h2v5a3,3,0,0,0,3,3h1a3,3,0,0,0,3-3A1,1,0,0,0,20,19Z'/></g></svg>
                      </a>
                    </div>                        
                    <div class='post-shareIcon Linkedin'>
                      <a data-text='Linkedin' expr:href='&quot;https://www.linkedin.com/sharing/share-offsite/?url=&quot; + data:blog.url.canonical' rel='nofollow noreferrer' target='_blank'>
                        <svg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'><g><path d='M24,3H8A5,5,0,0,0,3,8V24a5,5,0,0,0,5,5H24a5,5,0,0,0,5-5V8A5,5,0,0,0,24,3Zm3,21a3,3,0,0,1-3,3H8a3,3,0,0,1-3-3V8A3,3,0,0,1,8,5H24a3,3,0,0,1,3,3Z'/><path d='M11,14a1,1,0,0,0-1,1v6a1,1,0,0,0,2,0V15A1,1,0,0,0,11,14Z'/><path d='M19,13a4,4,0,0,0-4,4v4a1,1,0,0,0,2,0V17a2,2,0,0,1,4,0v4a1,1,0,0,0,2,0V17A4,4,0,0,0,19,13Z'/><circle cx='11' cy='11' r='1'/></g></svg>
                      </a>
                    </div>                       
                    <div class='post-shareIcon beside'>
                      <a data-pin-config='beside' data-text='Pinterest' expr:href='&quot;https://pinterest.com/pin/create/button/?url=&quot; + data:blog.url.canonical + &quot;&amp;media=&quot; + data:blog.postImageUrl' rel='nofollow noreferrer' target='_blank'>
                        <svg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'><g><path d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26a12,12,0,0,1-3.81-.63l1.2-4.81A7.93,7.93,0,0,0,16,23a8.36,8.36,0,0,0,1.4-.12,8,8,0,1,0-9.27-6.49,1,1,0,0,0,2-.35,6,6,0,1,1,3.79,4.56L15,16.24A1,1,0,1,0,13,15.76l-2.7,10.81A12,12,0,1,1,16,28Z'/></g></svg>
                      </a>
                    </div>                      
                    <div class='post-shareIcon Telegram'>
                      <a data-text='Telegram' expr:href='&quot;https://t.me/share/url?url=&quot; + data:blog.url.canonical + &quot;&amp;text=&quot; + data:post.title' rel='nofollow noreferrer' target='_blank'>
                        <svg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'><g><path d='M24,28a1,1,0,0,1-.62-.22l-6.54-5.23a1.83,1.83,0,0,1-.13.16l-4,4a1,1,0,0,1-1.65-.36L8.2,18.72,2.55,15.89a1,1,0,0,1,.09-1.82l26-10a1,1,0,0,1,1,.17,1,1,0,0,1,.33,1l-5,22a1,1,0,0,1-.65.72A1,1,0,0,1,24,28Zm-8.43-9,7.81,6.25L27.61,6.61,5.47,15.12l4,2a1,1,0,0,1,.49.54l2.45,6.54,2.89-2.88-1.9-1.53A1,1,0,0,1,13,19a1,1,0,0,1,.35-.78l7-6a1,1,0,1,1,1.3,1.52Z'/></g></svg>
                      </a>
                    </div>                                          

                  </div>
                </b:includable>
   
    <b:includable id='altImage'>https://1.bp.blogspot.com/-q0slX7ll8do/X8KmpxKmlbI/AAAAAAAAARM/JJ0bD_ZbMTUOwBWwf2arZqxMm3AS2kP9ACLcBGAsYHQ/s1000-e90/default.png</b:includable>
    <b:includable id='responsiveImage'>
        <img class='lazy' expr:alt='data:post.title' expr:data-src='data:image ? data:image : data:imageAlt' expr:height='data:height' expr:src='data:imageLazy' expr:title='data:post.title' expr:width='data:width'/>
      </b:includable>
     <b:includable id='responsiveImageL'>
        <img expr:alt='data:post.title' expr:data-sizes='data:sourceSizes ?: &quot;&quot;' expr:data-srcset='sourceSet((data:image ? data:image : data:imageAlt), (data:imageSizes ?: [120,240,480,640,800]), data:imageRatio)' expr:height='data:height' expr:src='data:image ? data:image : data:imageAlt'/>
      </b:includable>

    <b:includable id='PostLabel'>     
   <div class='post-label'>
             <b:loop index='num' values='data:post.labels' var='label'>
               <b:if cond='data:num == 0'>
                 <a expr:href='data:label.url' rel='tag'><data:label.name/></a><b:if cond='data:label.isLast != &quot;true&quot;'/>
               </b:if>
               <b:if cond='data:num == 1'>
                 <a expr:href='data:label.url' rel='tag'><data:label.name/></a><b:if cond='data:label.isLast != &quot;true&quot;'/>
               </b:if>
             </b:loop>
           </div>
      </b:includable>
    <b:includable id='Author'>
        <div class='Author'>
          <div class='left'>
            <img class='lazy' expr:alt='data:post.author.name' expr:data-src='resizeImage(data:post.author.authorPhoto.image,100,&quot;1:1&quot;)' expr:title='data:post.author.name' src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII='/>
          </div>
          <div class='right'>
            <div class='author-name'><svg aria-hidden='true' class='svg-inline--fa fa-user-circle fa-w-16' data-icon='user-circle' data-prefix='far' focusable='false' role='img' viewBox='0 0 496 512' xmlns='http://www.w3.org/2000/svg'><path d='M248 104c-53 0-96 43-96 96s43 96 96 96 96-43 96-96-43-96-96-96zm0 144c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm0-240C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm0 448c-49.7 0-95.1-18.3-130.1-48.4 14.9-23 40.4-38.6 69.6-39.5 20.8 6.4 40.6 9.6 60.5 9.6s39.7-3.1 60.5-9.6c29.2 1 54.7 16.5 69.6 39.5-35 30.1-80.4 48.4-130.1 48.4zm162.7-84.1c-24.4-31.4-62.1-51.9-105.1-51.9-10.2 0-26 9.6-57.6 9.6-31.5 0-47.4-9.6-57.6-9.6-42.9 0-80.6 20.5-105.1 51.9C61.9 339.2 48 299.2 48 256c0-110.3 89.7-200 200-200s200 89.7 200 200c0 43.2-13.9 83.2-37.3 115.9z' fill='currentColor'/></svg> <b:if cond='data:post.author.profileUrl'>
                <a expr:href='data:post.author.profileUrl' expr:title='data:post.author.name' rel='nofollow'>
                  <data:post.author.name/>
                </a>
                <b:else/>
                <data:post.author.name/>
              </b:if>
            </div>
            <div class='author-desc'>
              <data:post.author.aboutMe/>
            </div>
          </div>
        </div>
      </b:includable>   
    <b:includable id='widget-title'>
    <b:if cond='data:widget.sectionId'>


   <b:if cond='data:title contains &quot;[ASW]&quot;'>   
      
<b:if cond='data:title.length gt 0'>
  
  <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3>
    <b:loop index='i' values='data:labels' var='label'><b:if cond='data:i == 0'>
  
  <b:if cond='data:title contains &quot;[ASW]&quot;'>
  
<a class='Lapel-Link' expr:href='data:blog.canonicalHomepageUr + &quot;/search/label/&quot; + data:label.name'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Show more<b:else/>عرض المزيد
</b:if></a><b:else/>
      </b:if></b:if></b:loop></div></b:if>
        <b:else/>
      <b:if cond='data:title.length gt 0'><div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div></b:if>
    </b:if>      
      <b:else/>
      <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div>
       </b:if>
    </b:includable>
    </b:defaultmarkup>
    <b:defaultmarkup type='Label'>
    <b:includable id='ASW'>
      
<b:loop index='i' values='data:labels' var='label'><b:if cond='data:i == 0'><div class='ASW' expr:labels='data:label.name'>
    <b:class cond='data:title contains &quot;[V]&quot;' name='video'/>
 <div class='loading'/></div>
  </b:if></b:loop>    </b:includable>
       
    <b:includable id='default'>
   <div class='widget-content'>
    <b:class expr:name='data:this.display + &quot;-label-widget-content&quot;'/>
    <b:include cond='data:this.display == &quot;list&quot;' name='list'/>
    <b:include cond='data:this.display == &quot;cloud&quot;' name='cloud'/>
  </div>
    </b:includable>
    <b:includable id='content'>
    <b:if cond='data:title contains &quot;[ASW]&quot;'><b:include name='ASW'/>   
      
            <b:else/>
      
        <b:include name='default'/>
      </b:if>
      
      
    </b:includable>
      

      
   </b:defaultmarkup>
    <b:defaultmarkup type='LinkList'>
       <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
       <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
    </b:defaultmarkup>
    <b:defaultmarkup type='HTML'>
    <b:includable id='main'>            
  <b:if cond='data:title contains &quot;[ADS]&quot;'>
  <div class='Ads-content'><data:content/></div>  
   <b:else/> 
  <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div>
  <div class='widget-content'><data:content/></div>
</b:if> 
  
  
</b:includable>

    </b:defaultmarkup>
 <b:defaultmarkup type='Header'>
      <b:widget-settings>
        <b:widget-setting name='displayUrl'/>
        <b:widget-setting name='displayHeight'>0</b:widget-setting>
        <b:widget-setting name='sectionWidth'>-1</b:widget-setting>
        <b:widget-setting name='useImage'>false</b:widget-setting>
        <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
        <b:widget-setting name='imagePlacement'>BEHIND</b:widget-setting>
        <b:widget-setting name='displayWidth'>0</b:widget-setting>
      </b:widget-settings>
      <b:includable id='main' var='this'>
        <b:include cond='data:imagePlacement in {&quot;replace&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='image'/>
        <b:include cond='data:imagePlacement not in {&quot;replace&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='title'/>
        <b:include name='description'/>
        <b:include cond='data:imagePlacement == &quot;BEHIND&quot;' name='behindImageStyle'/>
      </b:includable>
      <b:includable id='description'>
        <div class='description'>
          <data:this.description/>
        </div>
      </b:includable>
      <b:includable id='behindImageStyle'>
        <b:if cond='data:sourceUrl'>
          <b:include cond='data:this.image' data='{image: data:this.image, selector: &quot;#header .widget&quot;}' name='responsiveImageStyle'/>
        </b:if>
      </b:includable>
      <b:includable id='image'>
        <a expr:href='data:blog.homepageUrl' expr:title='data:title'><img expr:alt='data:title' expr:src='resizeImage(data:sourceUrl, 300)' expr:title='data:title'/></a>
        <b:include cond='data:this.imagePlacement == &quot;replace&quot;' name='title'/>
      </b:includable>
      <b:includable id='title'>
        <div>
          <b:class cond='data:this.imagePlacement == &quot;replace&quot;' name='replaced'/>
          <b:if cond='data:view.isSingleItem'>
            <h2>
              <a expr:href='data:blog.homepageUrl' expr:title='data:title'><data:title/></a>
            </h2>
            <b:elseif cond='data:view.isHomepage'/>
              <h1><data:title/></h1>
            <b:else/>
            <h1>
              <a expr:href='data:blog.homepageUrl' expr:title='data:title'><data:title/></a>
            </h1>
          </b:if>
        </div>
      </b:includable>
  </b:defaultmarkup>
  </b:defaultmarkups>
<b:tag name='script' type='text/javascript'>
let LiveEnd = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Game Over&quot;<b:else/>&quot;إنتهت&quot;</b:if>
let LiveNot = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Not started yet&quot;<b:else/>&quot;لم تبدأ بعد&quot;</b:if>
let LiveNow = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Live Now&quot;<b:else/>&quot;جارية الان&quot;</b:if>
let LiveSoon = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Soon&quot;<b:else/>&quot;بعد قليل&quot;</b:if>
let Nopost = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;All Posts Loaded!&quot;<b:else/>&quot;لا يوجد المزيد&quot;</b:if>
let Showmore = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Show more&quot;<b:else/>&quot;عرض المزيد&quot;</b:if>
let Loading = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Loading...&quot;<b:else/>&quot;جاري العرض&quot;</b:if>
let TomorrowNot = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;No Matches Tomorrow&quot;<b:else/>&quot;لا يوجد مباريات الغد&quot;</b:if>
let TodayNot = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;No Matches Today&quot;<b:else/>&quot;لا يوجد مباريات اليوم&quot;</b:if>
let YesterdayNot = <b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;No Matches Yesterday&quot;<b:else/>&quot;لا يوجد مباريات الامس&quot;</b:if>
/*<![CDATA[*/
let  GetJs=(j,f,D)=>{var k =document["createElement"]("script");(k["src"] = j),(k["onload"] = function () {f();});if (D) k[D] = D;document["head"]["append"](k);}
let getJSONP =(j, f)=>{let D = document["createElement"]("script"),k = new URLSearchParams(j)["get"]("callback");!k && ((k = "AN_" + Math["round"](Math["random"]() * 0xe8d4a51000)), (j += "&callback=" + k)),(D["src"] = j),(window[k] = function (q) {f(q);}),document["head"]["append"](D);};

/*]]>*/</b:tag>

  </head>
<body expr:class='data:blog.languageDirection' expr:data-id='data:blog.blogId'>
<b:if cond='data:view.description.escaped contains &quot;Player-&quot;'><b:else/><b:tag name='script' type='text/javascript'>/*<![CDATA[*/if (localStorage.getItem('theme') === 'Dark') {document.body.classList.add('Dark');}
/*]]>*/</b:tag></b:if>
<b:class cond='data:view.isMultipleItems' name='multipleItems'/>
<b:class cond='data:view.isHomepage' name='Home'/>
<b:class cond='data:view.isSingleItem' name='singleItem'/>
<b:class cond='data:view.isPage' name='Page'/>
<b:class cond='data:view.isPost' name='Post'/>
<b:class cond='data:view.isError' name='Error-404'/> 
<b:class cond='data:blog.view == &quot;Live&quot;' name='Live'/> 
<b:class cond='data:blog.view == &quot;T&quot;' name='Table'/> 
<b:class cond='data:skin.vars.StyleSite == &quot;2px&quot;' name='StyleSite1'/> 
<b:class cond='data:view.description.escaped contains &quot;Player-&quot;' name='Player'/>

<!-- Setting -->  
<div class='Setting'><b:section id='Setting' showaddelement='no'>
  <b:widget id='LinkList3' locked='true' title='الاعدادات' type='LinkList' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='link-5'>1</b:widget-setting>
      <b:widget-setting name='link-3'>N</b:widget-setting>
      <b:widget-setting name='link-4'>N</b:widget-setting>
      <b:widget-setting name='text-1'>Safe2</b:widget-setting>
      <b:widget-setting name='text-0'>ASW</b:widget-setting>
      <b:widget-setting name='text-3'>Safe0</b:widget-setting>
      <b:widget-setting name='text-2'>Safe1</b:widget-setting>
      <b:widget-setting name='text-5'>Load_image</b:widget-setting>
      <b:widget-setting name='text-4'>Dark</b:widget-setting>
      <b:widget-setting name='shownum'>100000000</b:widget-setting>
      <b:widget-setting name='sorting'>NONE</b:widget-setting>
      <b:widget-setting name='link-1'>N</b:widget-setting>
      <b:widget-setting name='link-2'>N</b:widget-setting>
      <b:widget-setting name='link-0'>4</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
  <b:include name='content'/>
</b:includable>
    <b:includable id='content'>
<b:tag name='script' type='text/javascript'>let Settings={<b:loop index='i' values='data:links' var='link'>&#39;<data:link.name/>&#39;:&#39;<data:link.target/>&#39;,</b:loop>}</b:tag>
</b:includable>
  </b:widget>
</b:section>
 </div>
  

 <!-- AllSite -->  
<div class='AllSite'> <b:if cond='data:view.description.escaped contains &quot;Player-&quot;'><b:else/>
<b:if cond='data:blog.view != &quot;T&quot;'><header>
  <div>
<div class='Logo'><b:section class='Header' id='header' showaddelement='no'>
  <b:widget id='Header1' locked='true' title='سبورت جو الاصدار 2.8 (رأس الصفحة)' type='Header' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='displayUrl'>http://2.bp.blogspot.com/-4uYsaxGBsVY/YGeNHhSGe8I/AAAAAAAAAR0/Hxtoi_7V9A8-FTBscdkqrV1oNyi3wjsjQCK4BGAYYCw/s1600/logo.png</b:widget-setting>
      <b:widget-setting name='displayHeight'>80</b:widget-setting>
      <b:widget-setting name='sectionWidth'>542</b:widget-setting>
      <b:widget-setting name='useImage'>true</b:widget-setting>
      <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
      <b:widget-setting name='imagePlacement'>BEFORE_DESCRIPTION</b:widget-setting>
      <b:widget-setting name='displayWidth'>300</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main' var='this'>
        <b:include cond='data:imagePlacement in {&quot;replace&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='image'/>
        <b:include cond='data:imagePlacement not in {&quot;replace&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='title'/>
        <b:include name='description'/>
        <b:include cond='data:imagePlacement == &quot;BEHIND&quot;' name='behindImageStyle'/>
      </b:includable>
    <b:includable id='behindImageStyle'>
        <b:if cond='data:sourceUrl'>
          <b:include cond='data:this.image' data='{image: data:this.image, selector: &quot;#header .widget&quot;}' name='responsiveImageStyle'/>
        </b:if>
      </b:includable>
    <b:includable id='description'>
        <div class='description'>
          <data:this.description/>
        </div>
      </b:includable>
    <b:includable id='image'>
        <a expr:href='data:blog.homepageUrl' expr:title='data:title'><img expr:alt='data:title' expr:src='resizeImage(data:sourceUrl, 300)' expr:title='data:title' height='100%' width='200px'/></a>
        <b:include cond='data:this.imagePlacement == &quot;replace&quot;' name='title'/>
      </b:includable>
    <b:includable id='title'>
        <div>
          <b:class cond='data:this.imagePlacement == &quot;replace&quot;' name='replaced'/>
          <b:if cond='data:view.isSingleItem'>
            <h2>
              <a expr:href='data:blog.homepageUrl' expr:title='data:title'><data:title/></a>
            </h2>
            <b:elseif cond='data:view.isHomepage'/>
              <h1><data:title/></h1>
            <b:else/>
            <h1>
              <a expr:href='data:blog.homepageUrl' expr:title='data:title'><data:title/></a>
            </h1>
          </b:if>
        </div>
      </b:includable>
  </b:widget>
</b:section> </div>
<div class='Menu'><b:section class='Menut' id='Menut' showaddelement='no'>
  <b:widget id='LinkList2' locked='true' title='' type='LinkList' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='link-3'>http://</b:widget-setting>
      <b:widget-setting name='sorting'>NONE</b:widget-setting>
      <b:widget-setting name='text-1'>كورة</b:widget-setting>
      <b:widget-setting name='link-1'>http://</b:widget-setting>
      <b:widget-setting name='text-0'>شراء القالب</b:widget-setting>
      <b:widget-setting name='link-2'>http://</b:widget-setting>
      <b:widget-setting name='text-3'>مباريات</b:widget-setting>
      <b:widget-setting name='link-0'>http://</b:widget-setting>
      <b:widget-setting name='text-2'>مباريات</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
    <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
<li><span> <svg class='closeMenu' height='14' viewBox='0 0 14 14' width='14' xmlns='http://www.w3.org/2000/svg'><g fill='none' fill-rule='evenodd' stroke='#000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' transform='translate(1 1)'><path d='M12 0L0 12M0 0l12 12'/></g></svg></span></li><b:loop values='data:links' var='link'>
       <li><a class='notran' expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
  </b:widget>
  <b:widget id='LinkList8' locked='true' title='مواقع التواصل الاجتماعي[social]' type='LinkList' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='shownum'>734294560</b:widget-setting>
      <b:widget-setting name='link-3'>https://</b:widget-setting>
      <b:widget-setting name='sorting'>NONE</b:widget-setting>
      <b:widget-setting name='text-1'>facebook</b:widget-setting>
      <b:widget-setting name='link-1'>https://</b:widget-setting>
      <b:widget-setting name='text-0'>facebook</b:widget-setting>
      <b:widget-setting name='link-2'>https://</b:widget-setting>
      <b:widget-setting name='text-3'>telegram</b:widget-setting>
      <b:widget-setting name='link-0'>https://</b:widget-setting>
      <b:widget-setting name='text-2'>news</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
  <b:include name='content'/>
</b:includable>
    <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
  </b:widget>
</b:section>
    
   <div class='option'>
<div class='MenuX'><svg height='14' viewBox='0 0 20 14' width='20' xmlns='http://www.w3.org/2000/svg'><g fill='none' fill-rule='evenodd' stroke='#000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' transform='translate(1 1)'><path d='M0 6h18M0 0h18M0 12h18'/></g></svg></div>
       <div id='themeDark'><svg class='line svg-1' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><path d='M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z'/></svg> 
<svg class='line svg-2' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><circle cx='12' cy='12' r='5'/><line x1='12' x2='12' y1='1' y2='3'/><line x1='12' x2='12' y1='21' y2='23'/><line x1='4.22' x2='5.64' y1='4.22' y2='5.64'/><line x1='18.36' x2='19.78' y1='18.36' y2='19.78'/><line x1='1' x2='3' y1='12' y2='12'/><line x1='21' x2='23' y1='12' y2='12'/><line x1='4.22' x2='5.64' y1='19.78' y2='18.36'/><line x1='18.36' x2='19.78' y1='5.64' y2='4.22'/></svg></div>  
     </div>
    
    
    
    </div>
</div>  
 </header></b:if> </b:if>

<b:if cond='data:view.isHomepage or data:view.description.escaped contains &quot;matches-tomorrow&quot; or data:view.description.escaped contains &quot;matches-yesterday&quot; or data:blog.view == &quot;T&quot;'>
<b:section class='ADSTOPTable' id='ADSTOPTable' showaddelement='no'>
  <b:widget id='HTML15' locked='true' title='اعلان فوق جدول المباريات[ADS]' type='HTML' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style=" margin: 0 auto;width:83%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>            
  <b:if cond='data:title contains &quot;[ADS]&quot;'>
  <div class='Ads-content'><data:content/></div>  
   <b:else/> 
  <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div>
  <div class='widget-content'><data:content/></div>
</b:if> 
  
  
</b:includable>
  </b:widget>
</b:section>
<div class='TabulaSoprt'>
  <div class='TabulaGo'>
<h2 class='boxstitle'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>important matches<b:else/>أهم المباريات</b:if></h2>
<b:if cond='not data:view.isLayoutMode'><ul class='nav-tabs'>
<li class='yesterday'><a href='/p/matches-yesterday.html'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Yesterday<b:else/>الأمس</b:if></a></li>
<li class='Today'><a expr:href='data:blog.homepageUrl'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Today<b:else/>اليوم</b:if></a></li>
<li class='tomorrow'><a href='/p/matches-tomorrow_21.html'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Tomorrow<b:else/>الغد</b:if></a></li>
</ul></b:if>
</div>
   <b:section class='tabulaSoprt' id='tabulaSoprt' showaddelement='no'>
     <b:widget cond='data:view.description.escaped contains &quot;matches-tomorrow&quot;' id='HTML22' locked='true' title='الغد' type='HTML' version='2' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='content'>&lt;div class=&quot;containerMatch&quot;&gt;&lt;a href=&quot;https://football.maxsp0orts.com/2021/12/Summary-of-the-goal-of-Manchesters-victory-over-Crystal-Palace-1-0-English-Premier-League.html&quot; title=&quot;كريستال بالاسvsمانشستر يونايتد&quot;&gt;&lt;div class=&quot;Match&quot;&gt;&lt;div class=&quot;fareq&quot;&gt;&lt;div class=&quot;Imagee&quot;&gt;&lt;img alt=&quot;مانشستر يونايتد&quot; class=&quot;col-img lazy&quot; height=&quot;70&quot;  src=&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=&quot;  data-src=&quot;https://ssl.gstatic.com/onebox/media/sports/logos/udQ6ns69PctCv143h-GeYw_96x96.png&quot; title=&quot;مانشستر يونايتد&quot; width=&quot;70&quot; /&gt;&lt;/div&gt;&lt;div class=&quot;asm&quot;&gt;مانشستر يونايتد&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;Nateja&quot;&gt;&lt;div class=&#39;matchHour&#39;&gt;55:00م&lt;/div&gt;&lt;div class=&quot;natej&quot;&gt;0-0&lt;/div&gt;&lt;div class=&quot;matchDate&quot; data-start=&#39;2021-12-05T16:00:00+02:00&#39; data-end=&#39;2021-12-05T18:00:00+02:00&#39;&gt;&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;fareq&quot;&gt;&lt;div class=&quot;Imagee&quot;&gt;&lt;img alt=&quot;كريستال بالاس&quot; class=&quot;col-img lazy&quot; height=&quot;70&quot;  data-src=&quot;https://ssl.gstatic.com/onebox/media/sports/logos/8piQOzndGmApKYTcvyN9vA_96x96.png&quot; 
 src=&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=&quot; title=&quot;كريستال بالاس&quot; width=&quot;70&quot; /&gt;&lt;/div&gt;&lt;div class=&quot;asm&quot;&gt;كريستال بالاس&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;Show&quot;&gt;&lt;/div&gt;&lt;ul&gt;&lt;li&gt;&lt;span&gt;&lt;/span&gt;beIN Sports Premium 1&lt;/li&gt;&lt;li&gt;&lt;span&gt;&lt;/span&gt;رؤوف خليف&lt;/li&gt;&lt;li&gt;&lt;span&gt;&lt;/span&gt;الدوري الانجليزي&lt;/li&gt;&lt;/ul&gt;&lt;/a&gt;&lt;/div&gt;</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>            
  <div class='Tomorrow'><data:content/></div>
</b:includable>
     </b:widget>
     <b:widget cond='data:view.isHomepage' id='HTML2' locked='true' title='اليوم' type='HTML' version='2' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='content'>&lt;div class=&quot;containerMatch&quot;&gt;&lt;a href=&quot;https://football.maxsp0orts.com/2021/12/Summary-of-the-goal-of-Manchesters-victory-over-Crystal-Palace-1-0-English-Premier-League.html&quot; title=&quot;كريستال بالاسvsمانشستر يونايتد&quot;&gt;&lt;div class=&quot;Match&quot;&gt;&lt;div class=&quot;fareq&quot;&gt;&lt;div class=&quot;Imagee&quot;&gt;&lt;img alt=&quot;مانشستر يونايتد&quot; class=&quot;col-img lazy&quot; height=&quot;70&quot;  src=&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=&quot;  data-src=&quot;https://ssl.gstatic.com/onebox/media/sports/logos/udQ6ns69PctCv143h-GeYw_96x96.png&quot; title=&quot;مانشستر يونايتد&quot; width=&quot;70&quot; /&gt;&lt;/div&gt;&lt;div class=&quot;asm&quot;&gt;مانشستر يونايتد&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;Nateja&quot;&gt;&lt;div class=&#39;matchHour&#39;&gt;44:00م&lt;/div&gt;&lt;div class=&quot;natej&quot;&gt;0-0&lt;/div&gt;&lt;div class=&quot;matchDate&quot; data-start=&#39;2021-12-05T16:00:00+02:00&#39; data-end=&#39;2021-12-05T18:00:00+02:00&#39;&gt;&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;fareq&quot;&gt;&lt;div class=&quot;Imagee&quot;&gt;&lt;img alt=&quot;كريستال بالاس&quot; class=&quot;col-img lazy&quot; height=&quot;70&quot;  data-src=&quot;https://ssl.gstatic.com/onebox/media/sports/logos/8piQOzndGmApKYTcvyN9vA_96x96.png&quot; 
 src=&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=&quot; title=&quot;كريستال بالاس&quot; width=&quot;70&quot; /&gt;&lt;/div&gt;&lt;div class=&quot;asm&quot;&gt;كريستال بالاس&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;Show&quot;&gt;&lt;/div&gt;&lt;ul&gt;&lt;li&gt;beIN Sports Premium 1&lt;/li&gt;&lt;li&gt;رؤوف خليف&lt;/li&gt;&lt;li&gt;الدوري الانجليزي&lt;/li&gt;&lt;/ul&gt;&lt;/a&gt;&lt;/div&gt;</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>            

  <div class='Today'><data:content/></div>
 
  
  
</b:includable>
     </b:widget>
     <b:widget cond='data:view.description.escaped contains &quot;matches-yesterday&quot;' id='HTML1' locked='true' title='الامس' type='HTML' version='2' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='content'>الامس</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>            
  <div class='Yesterday'><data:content/></div>

</b:includable>
     </b:widget>
   </b:section> 
  </div>
<b:if cond='data:blog.view == &quot;T&quot;'><div style='display:none;'><a class='Anubis-Web'/></div></b:if>
  </b:if>
  
<b:if cond='data:blog.view != &quot;T&quot;'>
<b:section class='PostsTop' cond='data:view.isHomepage' id='PostsTop'>
  <b:widget id='HTML14' locked='true' title='اعلان اسفل جدول المباريات[ADS]' type='HTML' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style=" margin: 0 auto;width:83%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>            
  <b:if cond='data:title contains &quot;[ADS]&quot;'>
  <div class='Ads-content'><data:content/></div>  
   <b:else/> 
  <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div>
  <div class='widget-content'><data:content/></div>
</b:if> 
  
  
</b:includable>
  </b:widget>
  <b:widget id='Label3' locked='false' title='مميز[ASW][V]' type='Label' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
      <b:widget-setting name='display'>LIST</b:widget-setting>
      <b:widget-setting name='selectedLabelsList'/>
      <b:widget-setting name='showType'>ALL</b:widget-setting>
      <b:widget-setting name='showFreqNumbers'>false</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main' var='this'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
    <b:includable id='ASW'>
      
<b:loop index='i' values='data:labels' var='label'><b:if cond='data:i == 0'><div class='ASW' expr:labels='data:label.name'>
    <b:class cond='data:title contains &quot;[V]&quot;' name='video'/>
 <div class='loading'/></div>
  </b:if></b:loop>    </b:includable>
    <b:includable id='cloud'>
  <b:loop values='data:labels' var='label'>
    <span class='label-size'>
      <b:class expr:name='&quot;label-size-&quot; + data:label.cssSize'/>
      <a class='label-name' expr:href='data:label.url'>
        <data:label.name/>
        <b:if cond='data:this.showFreqNumbers'>
          <span class='label-count'><data:label.count/></span>
        </b:if>
      </a>
    </span>
  </b:loop>
</b:includable>
    <b:includable id='content'>
    <b:if cond='data:title contains &quot;[ASW]&quot;'><b:include name='ASW'/>   
      
            <b:else/>
      
        <b:include name='default'/>
      </b:if>
      
      
    </b:includable>
    <b:includable id='default'>
   <div class='widget-content'>
    <b:class expr:name='data:this.display + &quot;-label-widget-content&quot;'/>
    <b:include cond='data:this.display == &quot;list&quot;' name='list'/>
    <b:include cond='data:this.display == &quot;cloud&quot;' name='cloud'/>
  </div>
    </b:includable>
    <b:includable id='list'>
  <ul>
    <b:loop values='data:labels' var='label'>
      <li>
        <a class='label-name' expr:href='data:label.url'>
          <data:label.name/>
          <b:if cond='data:this.showFreqNumbers'>
            <span class='label-count'><data:label.count/></span>
          </b:if>
        </a>
      </li>
    </b:loop>
  </ul>
</b:includable>
  </b:widget>
</b:section> 
<b:section cond='data:view.isPost' id='ADSTopPost' showaddelement='no'>
   <b:widget id='HTML5' locked='true' title='اعلان فوق المقالة فقط[ADS]' type='HTML' version='2' visible='true'>
     <b:widget-settings>
       <b:widget-setting name='content'><![CDATA[<ads style=" margin: 0 auto;width:83%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
     </b:widget-settings>
     <b:includable id='main'>            
  <b:if cond='data:title contains &quot;[ADS]&quot;'>
  <div class='AdsContent'><data:content/></div>  
   <b:else/> 
  <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div>
  <div class='widget-content'><data:content/></div>
</b:if> 
  
  
</b:includable>
   </b:widget>
 </b:section>
<main class='centered-bottom' id='main' role='main'>
                <b:if cond='data:view.isHomepage'>
                  <div class='headline'><h3 class='title'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Latest Topics<b:else/>آخر المواضيع</b:if></h3><a class='Lapel-Link' href='/search/'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Show more<b:else/>عرض المزيد</b:if></a></div>
                </b:if>
                <b:if cond='data:view.isArchive or (data:view.isSearch and data:view.search.resultsMessageHtml)'>
                  <div class='post-filter-message'>
                    <div>
                      <b:if cond='data:view.isArchive'>
                        <data:view.archive.rangeMessage/>
                      <b:elseif cond='data:view.isSearch and data:view.search.resultsMessageHtml'/>
                        <data:view.search.resultsMessageHtml/>
                      </b:if>
                    </div>
            <a class='flat-button ripple' expr:href='data:blog.homepageUrl'> <data:messages.showAll/></a>
                
                  </div>
                </b:if>
                <b:section class='main' id='page_body' name='Page Body' showaddelement='false'>
                  <b:widget id='Blog1' locked='true' title='رسائل المدونة الإلكترونية' type='Blog' version='2' visible='true'>
                    <b:widget-settings>
                      <b:widget-setting name='showDateHeader'>false</b:widget-setting>
                      <b:widget-setting name='commentLabel'>تعليق</b:widget-setting>
                      <b:widget-setting name='style.textcolor'>#0066cc</b:widget-setting>
                      <b:widget-setting name='showShareButtons'>true</b:widget-setting>
                      <b:widget-setting name='authorLabel'>بواسطة</b:widget-setting>
                      <b:widget-setting name='showCommentLink'>true</b:widget-setting>
                      <b:widget-setting name='style.urlcolor'>#ffffff</b:widget-setting>
                      <b:widget-setting name='showAuthor'>true</b:widget-setting>
                      <b:widget-setting name='style.linkcolor'>#e8e8e8</b:widget-setting>
                      <b:widget-setting name='style.unittype'>TextAndImage</b:widget-setting>
                      <b:widget-setting name='style.bgcolor'>#000000</b:widget-setting>
                      <b:widget-setting name='timestampLabel'/>
                      <b:widget-setting name='reactionsLabel'/>
                      <b:widget-setting name='showAuthorProfile'>false</b:widget-setting>
                      <b:widget-setting name='style.layout'>1x1</b:widget-setting>
                      <b:widget-setting name='showLabels'>true</b:widget-setting>
                      <b:widget-setting name='showLocation'>false</b:widget-setting>
                      <b:widget-setting name='postLabelsLabel'/>
                      <b:widget-setting name='showTimestamp'>true</b:widget-setting>
                      <b:widget-setting name='postsPerAd'>1</b:widget-setting>
                      <b:widget-setting name='showBacklinks'>false</b:widget-setting>
                      <b:widget-setting name='style.bordercolor'>#000000</b:widget-setting>
                      <b:widget-setting name='showInlineAds'>false</b:widget-setting>
                      <b:widget-setting name='showReactions'>false</b:widget-setting>
                    </b:widget-settings>
                    <b:includable id='main'>
          <b:include name='noContentPlaceholder'/>

          <b:comment>Cap the total number of ads (widgets and inline ads).</b:comment>
          <b:with value='3' var='maxNumAds'>
          <b:with value='data:widgets.AdSense.size' var='numDesktopAds'>
          <b:with value='data:widgets.AdSense count (w =&gt; w.sectionId != &quot;ads&quot;)' var='numMobileAds'>
          <b:comment>Filter out the featured post, but only on the homepage.</b:comment>
          <b:with value='data:widgets.FeaturedPost filter (w =&gt; w.sectionId == &quot;page_body&quot;) map (w =&gt; w.postId)' var='featuredPostIds'>
          <b:with value='data:view.isHomepage                                          ? data:posts filter (post =&gt; post.id not in data:featuredPostIds)                                          : data:posts' var='posts'>
            <b:include name='super.main'/>
          </b:with>
          </b:with>
          </b:with>
          </b:with>
          </b:with>
        </b:includable>
                    <b:includable id='aboutPostAuthor'>
  <div class='author-name'>
    <a class='g-profile' expr:href='data:post.author.profileUrl' rel='author' title='author profile'>
      <span>
        <data:post.author.name/>
      </span>
    </a>
  </div>
  <div>
    <span class='author-desc'>
      <data:post.author.aboutMe/>
    </span>
  </div>
</b:includable>
                    <b:includable id='addComments'>
  <a expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick'>
    <b:message name='messages.postAComment'/>
  </a>
</b:includable>
                    <b:includable id='blogThisShare'>
  <b:with value='&quot;window.open(this.href, \&quot;_blank\&quot;, \&quot;height=270,width=475\&quot;); return false;&quot;' var='onclick'>
    <b:include name='platformShare'/>
  </b:with>
</b:includable>
                    <b:includable id='bylineByName' var='byline'>
  <b:switch var='data:byline.name'>
  <b:case value='share'/>
    <b:include cond='data:post.shareUrl' name='postShareButtons'/>
  <b:case value='comments'/>
    <b:include cond='data:post.allowComments' name='postCommentsLink'/>
  <b:case value='location'/>
    <b:include cond='data:post.location' name='postLocation'/>
  <b:case value='timestamp'/>
    <b:include cond='not data:view.isPage' name='postTimestamp'/>
  <b:case value='author'/>
    <b:include name='postAuthor'/>
  <b:case value='labels'/>
    <b:include cond='data:post.labels' name='postLabels'/>
  <b:case value='icons'/>
    <b:include cond='data:post.emailPostUrl' name='emailPostIcon'/>
  </b:switch>
</b:includable>
                    <b:includable id='bylineRegion' var='regionItems'>
  <b:loop values='data:regionItems' var='byline'>
    <b:include data='byline' name='bylineByName'/>
  </b:loop>
</b:includable>
                    <b:includable id='commentAuthorAvatar'>
  <div class='avatar-image-container'>
    <img class='author-avatar' expr:src='data:comment.authorAvatarSrc' height='35' width='35'/>
  </div>
</b:includable>
                    <b:includable id='commentDeleteIcon' var='comment'>
  <span expr:class='&quot;item-control &quot; + data:comment.adminClass'>
    <b:if cond='data:showCmtPopup'>
      <div class='goog-toggle-button'>
        <div class='goog-inline-block comment-action-icon'/>
      </div>
    <b:else/>
      <a class='comment-delete' expr:href='data:comment.deleteUrl' expr:title='data:messages.deleteComment'>
        <img src='https://resources.blogblog.com/img/icon_delete13.gif'/>
      </a>
    </b:if>
  </span>
</b:includable>
                    <b:includable id='commentForm' var='post'>
  <div class='comment-form'>
    <a name='comment-form'/>
    <h4 id='comment-post-message'><data:messages.postAComment/></h4>
    <b:if cond='data:this.messages.blogComment != &quot;&quot;'>
      <p><data:this.messages.blogComment/></p>
    </b:if>
    <b:include data='post' name='commentFormIframeSrc'/>
    <iframe allowtransparency='allowtransparency' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight ?: &quot;90px&quot;' frameborder='0' id='comment-editor' name='comment-editor' src='' width='100%'/>
    <data:post.cmtfpIframe/>
    <script type='text/javascript'>
      BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);
    </script>
  </div>
</b:includable>
                    <b:includable id='commentFormIframeSrc' var='post'>
  <a expr:href='data:post.commentFormIframeSrc appendParams {skin: &quot;contempo&quot;}' id='comment-editor-src'/>
</b:includable>
                    <b:includable id='commentItem' var='comment'>
  <div class='comment' expr:id='&quot;c&quot; + data:comment.id'>
    <b:include cond='data:blog.enabledCommentProfileImages' name='commentAuthorAvatar'/>

    <div class='comment-block'>
      <div class='comment-author'>
        <b:if cond='data:comment.authorUrl'>
          <b:message name='messages.authorSaidWithLink'>
            <b:param expr:value='data:comment.author' name='authorName'/>
            <b:param expr:value='data:comment.authorUrl' name='authorUrl'/>
          </b:message>
        <b:else/>
          <b:message name='messages.authorSaid'>
            <b:param expr:value='data:comment.author' name='authorName'/>
          </b:message>
        </b:if>
      </div>
      <div expr:class='&quot;comment-body&quot; + (data:comment.isDeleted ? &quot; deleted&quot; : &quot;&quot;)'>
        <data:comment.body/>
      </div>
      <div class='comment-footer'>
        <span class='comment-timestamp'>
          <a expr:href='data:comment.url' title='comment permalink'>
            <data:comment.timestamp/>
          </a>
          <b:include data='comment' name='commentDeleteIcon'/>
        </span>
      </div>
    </div>
  </div>
</b:includable>
                    <b:includable id='commentList' var='comments'>
  <div id='comments-block'>
    <b:loop values='data:comments' var='comment'>
      <b:include data='comment' name='commentItem'/>
    </b:loop>
  </div>
</b:includable>
                    <b:includable id='commentPicker' var='post'>
  <b:if cond='data:post.showThreadedComments'>
    <b:include data='post' name='threadedComments'/>
  <b:else/>
    <b:include data='post' name='comments'/>
  </b:if>
</b:includable>
                    <b:includable id='comments' var='post'>
 <b:if cond='data:skin.vars.HideComment != &quot;2px&quot;'> <section expr:class='&quot;comments&quot; + (data:post.embedCommentForm ? &quot; embed&quot; : &quot;&quot;)' expr:data-num-comments='data:post.numberOfComments' id='comments'>
    <b:if cond='data:post.allowComments'>

      <div class='commentsTitle'><b:include name='commentsTitle'/></div>

      <div expr:id='data:widget.instanceId + &quot;_comments-block-wrapper&quot;'>
        <b:include cond='data:post.comments' data='post.comments' name='commentList'/>
      </div>

      <b:if cond='data:post.commentPagingRequired'>
        <div class='paging-control-container'>
          <b:if cond='data:post.hasOlderLinks'>
            <a expr:class='data:post.oldLinkClass' expr:href='data:post.oldestLinkUrl'>
              <data:messages.oldest/>
            </a>
            <a expr:class='data:post.oldLinkClass' expr:href='data:post.olderLinkUrl'>
              <data:messages.older/>
            </a>
          </b:if>

          <span class='comment-range-text'>
            <data:post.commentRangeText/>
          </span>

          <b:if cond='data:post.hasNewerLinks'>
            <a expr:class='data:post.newLinkClass' expr:href='data:post.newerLinkUrl'>
              <data:messages.newer/>
            </a>
            <a expr:class='data:post.newLinkClass' expr:href='data:post.newestLinkUrl'>
              <data:messages.newest/>
            </a>
          </b:if>
        </div>
      </b:if>

      <div class='footer'>
        <b:if cond='data:post.embedCommentForm'>
          <b:if cond='data:post.allowNewComments'>
            <b:include data='post' name='commentForm'/>
          <b:else/>
            <data:post.noNewCommentsText/>
          </b:if>
        <b:else/>
          <b:if cond='data:post.allowComments'>
            <b:include data='post' name='addComments'/>
          </b:if>
        </b:if>
      </div>
    </b:if>
    <b:if cond='data:showCmtPopup'>
      <div id='comment-popup'>
        <iframe allowtransparency='allowtransparency' frameborder='0' id='comment-actions' name='comment-actions' scrolling='no'>
        </iframe>
      </div>
    </b:if>
  </section></b:if>
</b:includable>
                    <b:includable id='commentsLink'>
          <a class='comment-link' expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick'>
            <b:include data='{ iconClass: &quot;touch-icon&quot; }' name='commentIcon'/>
            <span class='num_comments'>
              <b:if cond='data:post.numberOfComments &gt; 0'>
                <b:message name='messages.numberOfComments'>
                  <b:param expr:value='data:post.numberOfComments' name='numComments'/>
                </b:message>
              <b:else/>
                <data:messages.postAComment/>
              </b:if>
            </span>
          </a>
        </b:includable>
                    <b:includable id='commentsLinkIframe'>
  <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
</b:includable>
                    <b:includable id='commentsTitle'>
  <h3 class='title'><data:messages.comments/></h3>
</b:includable>
                    <b:includable id='defaultAdUnit'>
          <b:comment>Clear out style (needs to be a non-empty string)</b:comment>
          <b:with value='&quot;/* Done in css. */&quot;' var='style'>
            <b:include name='super.defaultAdUnit'/>
          </b:with>
        </b:includable>
                    <b:includable id='emailPostIcon'>
  <span class='byline post-icons'>
    <!-- email post links -->
    <span class='item-action'>
      <a expr:href='data:post.emailPostUrl' expr:title='data:messages.emailPost'>
        <b:include data='{ iconClass: &quot;touch-icon sharing-icon&quot; }' name='emailIcon'/>
      </a>
    </span>
  </span>
</b:includable>
                    <b:includable id='facebookShare'>
  <b:with value='&quot;window.open(this.href, \&quot;_blank\&quot;, \&quot;height=430,width=640\&quot;); return false;&quot;' var='onclick'>
    <b:include name='platformShare'/>
  </b:with>
</b:includable>
                    <b:includable id='feedLinks'>
          <b:comment>Don&#39;t show feed links.</b:comment>
        </b:includable>
                    <b:includable id='feedLinksBody' var='links'>
  <div class='feed-links'>
  <data:messages.subscribeTo/>
  <b:loop values='data:links' var='f'>
     <a class='feed-link' expr:href='data:f.url' expr:type='data:f.mimeType' target='_blank'><data:f.name/> (<data:f.feedType/>)</a>
  </b:loop>
  </div>
</b:includable>
                    <b:includable id='footerBylines'>
  <b:if cond='data:widgets.Blog.first.footerBylines'>
    <b:loop index='i' values='data:widgets.Blog.first.footerBylines' var='region'>
      <b:if cond='not data:region.items.empty'>
        <div expr:class='&quot;post-footer-line post-footer-line-&quot; + (data:i + 1)'>
          <b:with value='&quot;footer-&quot; + (data:i + 1)' var='regionName'>
            <b:include data='region.items' name='bylineRegion'/>
          </b:with>
        </div>
      </b:if>
    </b:loop>
  </b:if>
</b:includable>
                    <b:includable id='googlePlusShare'>
  <div class='goog-inline-block google-plus-share-container'>
    <g:plusone annotation='inline' expr:href='data:originalUrl.canonical.http' size='medium' source='blogger:blog:plusone'/>
  </div>
</b:includable>
                    <b:includable id='headerByline'>
          <b:include cond='data:view.isMultipleItems or data:widgets.Blog.first.headerByline.items.share' data='{ shareButtonClass: &quot;post-share-buttons-top&quot;, overridden: true }' name='maybeAddShareButtons'/>
          <b:include name='super.headerByline'/>
        </b:includable>
                    <b:includable id='homePageLink'><b:comment>Don&#39;t show</b:comment></b:includable>
                    <b:includable id='iframeComments' var='post'>
  <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
</b:includable>
                    <b:includable id='inlineAd' var='post'>
          <div>
            <b:class cond='data:post.adNumber + data:numDesktopAds lt data:maxNumAds' name='desktop-ad'/>
            <b:class cond='data:post.adNumber + data:numMobileAds lt data:maxNumAds' name='mobile-ad'/>
            <b:include data='post' name='super.inlineAd'/>
          </div>
        </b:includable>
                    <b:includable id='linkShare'>
  <b:with value='&quot;window.prompt(\&quot;Copy to clipboard: Ctrl+C, Enter\&quot;, \&quot;&quot; + data:originalUrl + &quot;\&quot;); return false;&quot;' var='onclick'>
    <b:include name='platformShare'/>
  </b:with>
</b:includable>
                    <b:includable id='manageComments'>
  <a expr:href='data:post.manageCommentsUrl' expr:onclick='data:post.manageCommentsUrlOnclick'>
    <b:message name='messages.manageComments'/>
  </a>
</b:includable>
                    <b:includable id='nextPageLink'>
          <a class='blog-pager-older-link flat-button ripple' expr:href='data:olderPageUrl' expr:title='data:messages.morePosts'>
            <data:messages.morePosts/>
          </a>
        </b:includable>
                    <b:includable id='otherSharingButton'>
  <span class='sharing-platform-button sharing-element-other' expr:aria-label='data:messages.shareToOtherApps.escaped' expr:data-url='data:originalUrl' expr:title='data:messages.shareToOtherApps.escaped' role='menuitem' tabindex='-1'>
    <b:with value='{key: &quot;sharingOther&quot;}' var='platform'>
      <b:include name='sharingPlatformIcon'/>
    </b:with>
    <span class='platform-sharing-text'><data:messages.shareOtherApps.escaped/></span>
  </span>
</b:includable>
                    <b:includable id='platformShare'>
  <a expr:class='&quot;goog-inline-block sharing-&quot; + data:platform.key' expr:data-url='data:originalUrl' expr:href='data:shareUrl + &quot;&amp;target=&quot; + data:platform.target' expr:onclick='data:onclick ? data:onclick : &quot;&quot;' expr:title='data:platform.shareMessage' target='_blank'>
    <span class='share-button-link-text'>
      <data:platform.shareMessage/>
    </span>
  </a>
</b:includable>
                    <b:includable id='post' var='post'>
        <b:if cond='data:view.isSingleItem'>
  <b:if cond='data:view.isPost'> <div class='TeamVS'/></b:if>
                    <div class='PostContent'>
                      
<b:if cond='data:view.description.escaped contains &quot;Player-&quot;'>
             <b:else/>   
   <div class='PostTop'>
                <div class='Title'><h1 class='Title-Post'><data:post.title/></h1></div>  
                      <div class='Info'>
<div class='datetime'><svg aria-hidden='true' class='svg-inline--fa fa-calendar-alt fa-w-14' data-icon='calendar-alt' data-prefix='fas' focusable='false' role='img' viewBox='0 0 448 512' xmlns='http://www.w3.org/2000/svg'><path d='M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z' fill='currentColor'/></svg><time expr:datetime='data:post.date.iso8601' expr:title='data:post.date.iso8601'><b:eval expr='format(data:post.date, &quot; d MMMM, YYYY&quot;)'/></time></div>
           
<b:include cond='data:skin.vars.AuthorN != &quot;2px&quot;' name='Author'/>
                        
                       </div>
                     
                      </div>   
    </b:if>       

                  
                      
           <b:if cond='data:view.description.escaped contains &quot;Player-&quot;'><div class='Post-Player'>
<span id='tme'><div id='tme_message'><button class='clossalert' onclick='document.getElementById(&quot;tme&quot;).style.display=&quot;none&quot;;'>X</button><strong>اشترك في يلا شوت</strong><p>موقع يلا شوت لمشاهدة مباريات اليوم على تليجرام<a href='https://t.me/sportgooooooooo' rel='nofollow' role='button' target='_blank'>اضغط للاشتراك</a></p></div><svg fill='rgb(255, 255, 255)' height='25' viewBox='0 0 33 33' width='25'><path d='M27.1566711,3.01477064 C27.5924362,2.97926122 28.0620472,2.99321135 28.4483128,3.22233167 C28.7931173,3.42270627 28.9928078,3.82091907 29,4.2127909 C28.9860386,4.54505764 28.9610773,4.87647891 28.9314622,5.20747745 C28.7884635,6.88741129 28.4986585,8.55001316 28.2706222,10.2198015 C28.2126612,10.6691647 28.1411619,11.1164144 28.0730471,11.5645094 C27.7392425,13.7538344 27.3783613,15.938932 27.0136724,18.123607 C26.61683,20.4689197 26.2166029,22.8142325 25.8146836,25.1586998 C25.6979155,25.8283061 25.2393044,26.4133661 24.63981,26.7253417 C23.9823546,27.0791677 23.1370548,27.107068 22.4893301,26.7156189 C19.3458979,24.5871672 16.1800428,22.4904204 13.067495,20.3167365 C12.7709209,20.0707069 12.6004224,19.6864443 12.5902687,19.3034498 C12.5796919,18.9310236 12.7057676,18.5687429 12.8788045,18.2436626 C13.1246099,17.7875356 13.4774527,17.3982002 13.8717567,17.0655107 C15.3580121,15.608779 16.9293051,14.2433571 18.4578677,12.8318576 C19.4609737,11.9124172 20.4378492,10.9633857 21.3648021,9.96658545 C21.5188007,9.82581596 21.5653387,9.6110685 21.5996076,9.41407576 C21.6211843,9.29444282 21.5839539,9.13169131 21.4439167,9.10928655 C21.1934575,9.07884991 20.9607674,9.20820566 20.7390772,9.30670203 C20.1484673,9.61064577 19.5976263,9.98603109 19.0544006,10.3669119 C17.9654108,11.1202189 16.8844593,11.8857852 15.7997002,12.6450104 C13.566298,14.2036204 11.3159729,15.7470121 8.94549505,17.0925655 C8.29015502,17.4282141 7.54046972,17.5486925 6.80939963,17.5051512 C5.872293,17.4540007 4.96353226,17.1931756 4.07338673,16.9116366 C3.62916011,16.7700216 3.17774125,16.6470068 2.74789911,16.4643869 C1.99863688,16.1566386 1.21933647,15.9017317 0.527189087,15.4730822 C0.307614215,15.3238581 0.0643472572,15.1437746 0.00554011426,14.8694221 C-0.0405748395,14.567592 0.209884359,14.3279034 0.434113033,14.1693792 C0.977338727,13.8624763 1.53283354,13.5792464 2.08875142,13.2964392 C4.04419469,12.3220438 6.04532984,11.444031 8.04731114,10.5706684 C8.82872691,10.2312152 9.60464274,9.87950282 10.3919815,9.55442252 C12.9194195,8.49590356 15.4460112,7.43569368 17.9861414,6.4080341 C20.3625422,5.44336147 22.7461354,4.49602082 25.1415745,3.57953955 C25.7892992,3.31871439 26.4543699,3.07310755 27.1566711,3.01477064 Z'/></svg></span>

<span class='NameSitePlayer'><div><data:blog.title.escaped/></div></span> 
             
<style>/*<![CDATA[*/
span.NameSitePlayer {position: fixed;bottom: 20px;background: #00000075;border-radius: 10px;left: 0;padding: 1px 20px;right: 0;z-index: 1000000;text-align: center;display: table;margin: auto;font-size: 20px;color: #fff;}
#tme{position:fixed;z-index:99999999;bottom:50px;right:25px;width:48px;height:48px;text-align:center;border-radius:50%;background-color:#0088cc;cursor:pointer}
#tme_message{position:absolute;bottom:62px;right:-10px;width:250px;padding:10px;background:white;border-radius:4px;box-shadow:0 2px 5px rgb(0 0 0);transition:opacity .5s ease-in}
#tme_message:after,#tme_message:before{content:'';position:absolute;bottom:-21px;right:23px;border-style:solid;height:0;width:0;border-width:10px;border-color:transparent}
#tme_message:after{border-top-color:#fff;bottom:-20px}
#tme svg{margin-top:13px}
#tme_message p{margin:8px 0;font-size:12px;color:#8e8e8e;text-align:right}
.clossalert{position:absolute;right:8px}
#tme_message a{margin:2px 6px;font-weight:bold;color:#FFF;background:#0088cc;border-radius:22px;display:inline-block;padding:5px;text-decoration:none}
.share_channell{display: none;position:absolute;top:0px;z-index:9;left:0px;border-radius:0;background:#1c273391;right:-10px;bottom:0;height:100vh;text-align:center}
.share-channel{position:absolute;top:50%;right:0;left:0;margin-top:-50px}.share-channel_title{text-align:center;color:#fff;font-size:21px}.share-channel_title *{color:#fff}.custom-btn {background: var(--BGS);border: 0;font-size: 16px;padding: 5px 16px;border-radius: 5px;font-weight: 700;color: #fff;cursor: pointer;display: table;margin: 4px auto 0 auto;}.share-channel textarea{margin-top:13px;max-width:98%;width:600px;direction:ltr;overflow:auto;font-size:14px;padding:5px;background:rgba(255,255,255,0.26);border:0;height:54px;color:#fff}button.Reload, button.GetCode {position: fixed;top: 10px;left: 10px;z-index: 100;padding: 2px 10px;background: var(--BGS);color: #fff;border: 0;outline: 0;font-size: 18px;border-radius: 3px;cursor: pointer;}button.GetCode {right: 10px!important;left: auto;}.share_channell.Open {display: block!important;}
/*]]>*/</style>
             

<button class='Reload' onclick='javascript:window.location.reload()'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Reload<b:else/>تحديث</b:if></button>
<button class='GetCode'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Share<b:else/>مشاركة</b:if></button>

<div class='share_channell'><div class='share-channel'> <div class='share-channel_title'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Embed code<b:else/>كود التضمين</b:if></div><textarea id='player_share' onclick='this.select();' onfocus='this.select();'/><button class='custom-btn' onclick='document.querySelector(&apos;#player_share&apos;).select();document.execCommand(&apos;copy&apos;);'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Click to copy<b:else/>انقر للنسخ</b:if></button></div></div>

            
<b:tag name='script' type='text/javascript'>

let Share=<b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Share&quot;<b:else/>&quot;مشاركة&quot;</b:if>;
let Close=<b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>&quot;Close&quot;<b:else/>&quot;اغلاق&quot;</b:if>;
let imageplayer=&#39;https://1.bp.blogspot.com/-SW8rBrqCAFA/YR-5Me9n3eI/AAAAAAAAATo/bksBAisAFWkkc-4abEvnzNqgL8AvUQk9wCLcBGAsYHQ/s16000/tempv2.6.png&#39;;
/*<![CDATA[*/ 
document.querySelector(".GetCode").addEventListener("click", () => {
  const errion = {
    vmTkU: "Open"
  };
  errion.fjezW = "button.GetCode";
  errion.oCGqy = "اغلاق";
  errion.KySaW = "مشاركة";
  document.querySelector(".share_channell").classList.toggle("Open");
  if (document.querySelector(errion.fjezW).textContent == "مشاركة") {
    document.querySelector(errion.fjezW).textContent = errion.oCGqy;
  } else {
    document.querySelector("button.GetCode").textContent = errion.KySaW;
  }
});
document.querySelector("textarea#player_share").innerHTML = "<iframe allowfullscreen='true' frameborder='0' height='500px' scrolling='no' src='" + window.location.href + "' width='100%'></iframe>";
/*]]>*/</b:tag>
                          
             
<b:if cond='data:view.description.escaped contains &quot;Player-ArtPlayer&quot;'>
<div class='Player-ArtPlayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(ercell => ercell.json()).then(kiamesha => {
  document.body.setAttribute("data-id", kiamesha.feed.id.$t.split("-")[1]);
});
getJSONP(atob("aHR0cHM6Ly93d3cuYmxvZ2dlci5jb20vZmVlZHMvNDE0OTAxMjE0MzUzNjYzNDA0NS9wb3N0cy9kZWZhdWx0LzQxNTYxOTcxNjA2NzQwOTg3MTM/YWx0PWpzb24="), keyontae => {
  let ethany = keyontae.entry.content.$t.split("<!--clear-->");
  let kieffer = ethany[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (kieffer.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(ethany[0]));
    GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest", () => {
      GetJs("//cdn.jsdelivr.net/npm/artplayer/dist/artplayer.js", () => {});
    });
  } else {
    console.log(eval(ethany[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>
                             

<b:if cond='data:view.description.escaped contains &quot;Player-Bitmovin&quot;'>
<div id='Player-Bitmovin'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/

fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(carvin => carvin.json()).then(tayte => {
  document.body.setAttribute("data-id", tayte.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), hudson => {
  let nuria = hudson.entry.content.$t.split("<!--clear-->");
  let groves = nuria[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (groves.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(nuria[0]));
    GetJs("//cdn.bitmovin.com/player/web/8/bitmovinplayer.js", () => {
      function franca(jusuf) {
        if (jusuf.indexOf("licensing.bitmovin.com/licensing") > -1) {
          return "data:text/plain;charset=utf-8;base64,eyJzdGF0dXMiOiJncmFudGVkIiwibWVzc2FnZSI6IlRoZXJlIHlvdSBnby4ifQ==";
        }
        if (jusuf.indexOf("licensing.bitmovin.com/impression") > -1) {
          return "data:text/plain;charset=utf-8;base64,eyJzdGF0dXMiOiJncmFudGVkIiwibWVzc2FnZSI6IlRoZXJlIHlvdSBnby4ifQ==";
        }
        return jusuf;
      }
      var saraanne = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function () {
        var myrlene = franca(arguments[1]);
        arguments[1] = myrlene;
        return saraanne.apply(this, arguments);
      };
      var aaraya = {
        "autoplay": true,
        "muted": false
      };
      var jyshawn = {
        key: "11d3698c-efdf-42f1-8769-54663995de2b",
        playback: aaraya
      };
      var itan = {
        hls: untie(UrlM3u8)
      };
      var akeelah = document.getElementById("Player-Bitmovin");
      var juma = new bitmovin.player.Player(akeelah, jyshawn);
      juma.load(itan).then(function () {
        juma.play();
        console.log();
      });
    });
  } else {
    console.log(eval(nuria[0]));
    document.body.innerHTML = "";
  }
});
/*]]>*/</b:tag></b:if>


<b:if cond='data:view.description.escaped contains &quot;Player-Chimee&quot;'>
<div id='Player-Chimee'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(kayal => kayal.json()).then(betzabe => {
  document.body.setAttribute("data-id", betzabe.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), dannett => {
  let jehron = dannett.entry.content.$t.split("<!--clear-->");
  let natalyia = jehron[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (natalyia.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(jehron[0]));
    GetJs("//cdn.jsdelivr.net/npm/chimee@0.11.0/lib/index.browser.min.js", () => {
      GetJs("//anubiswb.github.io/Source_Code/Java_Script/JS_Temp/hlsjs-p2p-engine.min.js", () => {
        GetJs("//anubiswb.github.io/Source_Code/Java_Script/JS_Temp/<EMAIL>", () => {
          const malanii = {
            live: true
          };
          const shealan = {
            maxBufferSize: 0
          };
          shealan.maxBufferLength = 10;
          shealan.liveSyncDurationCount = 10;
          shealan.handler = window.ChimeeKernelHls;
          shealan.p2pConfig = malanii;
          const jamarrion = {
            hls: shealan
          };
          new Chimee({
            wrapper: "#Player-Chimee",
            src: untie(UrlM3u8),
            controls: true,
            kernels: jamarrion
          });
        });
      });
    });
  } else {
    console.log(eval(jehron[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>   
              
<b:if cond='data:view.description.escaped contains &quot;Player-Clappr1&quot;'>
<div id='Player-Clappr1'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(damona => damona.json()).then(rashanti => {
  document.body.setAttribute("data-id", rashanti.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), mellina => {
  let dayjah = mellina.entry.content.$t.split("<!--clear-->");
  let jassmine = dayjah[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (jassmine.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(dayjah[0]));
    GetJs("//cdn.jsdelivr.net/npm/clappr@latest/dist/clappr.min.js", () => {
      GetJs("//cdn.jsdelivr.net/gh/clappr/clappr-level-selector-plugin@latest/dist/level-selector.min.js", () => {});
    });
  } else {
    console.log(eval(dayjah[0]));
    document.body.innerHTML = "";
  }
});
/*]]>*/</b:tag></b:if>      
             
<b:if cond='data:view.description.escaped contains &quot;Player-Clappr&quot;'>
<div id='Player-Clappr'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(berrie => berrie.json()).then(lakyna => {
  document.body.setAttribute("data-id", lakyna.feed.id.$t.split("-")[1]);
});
getJSONP(atob("aHR0cHM6Ly93d3cuYmxvZ2dlci5jb20vZmVlZHMvNDE0OTAxMjE0MzUzNjYzNDA0NS9wb3N0cy9kZWZhdWx0LzQxNTYxOTcxNjA2NzQwOTg3MTM/YWx0PWpzb24="), kamorra => {
  let jondra = kamorra.entry.content.$t.split("<!--clear-->");
  let htoo = jondra[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (htoo.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(jondra[0]));
    GetJs("//cdn.jsdelivr.net/npm/clappr@latest/dist/clappr.min.js", () => {
      GetJs("//cdn.jsdelivr.net/clappr.level-selector/latest/level-selector.min.js", () => {
        GetJs("//cdn.jsdelivr.net/npm/clappr-ima-plugin@latest/dist/clappr-ima-plugin.min.js", () => {
          player = new Clappr.Player({
            source: untie(UrlM3u8),
            mimeType: "application/x-mpegURL",
            maxBufferLength: "20",
            autoPlay: true,
            height: "100%",
            width: "100%",
            poster: imageplayer,
            parentId: "#Player-Clappr",
            mediacontrol: {
              seekbar: "#0066cc",
              buttons: "#fff"
            }
          });
        });
      });
    });
  } else {
    console.log(eval(jondra[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>                
             
    
<b:if cond='data:view.description.escaped contains &quot;Player-Dplayer&quot;'>
<div id='Player-Dplayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(marchell => marchell.json()).then(nylyn => {
  document.body.setAttribute("data-id", nylyn.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), jonothon => {
  let averleigh = jonothon.entry.content.$t.split("<!--clear-->");
  let saudi = averleigh[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (saudi.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(averleigh[0]));
    GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest", () => {
      GetJs("//cdn.jsdelivr.net/npm/dplayer@1.26.0", () => {
        var sahanna = "normal";
        if (Hls.isSupported() && Hls.WEBRTC_SUPPORT) {
          sahanna = "customHls";
        }
      });
    });
  } else {
    console.log(eval(averleigh[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>   
  
  
               
<b:if cond='data:view.description.escaped contains &quot;Player-Flowplayer&quot;'>
<link href='https://releases.flowplayer.org/7.2.6/skin/skin.css' rel='stylesheet'/>
<div id='Player-Dplayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/fetch("//"+window.location.hostname+"/feeds/posts/default/?max-results=0&alt=json").then(response => response.json()).then(data =>{document.body.setAttribute('data-id',data.feed.id.$t.split('-')[1]);});getJSONP(atob('aHR0cHM6Ly93d3cuYmxvZ2dlci5jb20vZmVlZHMvNDE0OTAxMjE0MzUzNjYzNDA0NS9wb3N0cy9kZWZhdWx0LzQxNTYxOTcxNjA2NzQwOTg3MTM/YWx0PWpzb24='),(data)=> {let IdBloge = data.entry.content.$t.split('<!--clear-->'),ID= IdBloge[1].replaceAll(' ','').replaceAll('</li></ul>','').replaceAll('<ulclass="domains"><li>','').split('</li><li>');  if(ID.includes(document.body.getAttribute('data-id'))) {console.log(eval(IdBloge[0]));
GetJs('//cdn.jsdelivr.net/npm/cdnbye@latest/dist/hls.light.min.js',()=>{GetJs('//releases.flowplayer.org/7.2.6/flowplayer.min.js',()=>{
 flowplayer('#Player-Dplayer', {live: true,  ratio: 9/16, clip: {sources: [{ type: "application/x-mpegurl", src: untie(UrlM3u8)},]},hlsjs: {debug: false,p2pConfig: {live: true,getStats: function (totalP2PDownloaded, totalP2PUploaded, totalHTTPDownloaded) {var total = totalHTTPDownloaded + totalP2PDownloaded;},}}});
});});}else{console.log(eval(IdBloge[0]));document.body.innerHTML=``;}});/*]]>*/</b:tag></b:if>  
          
 
<b:if cond='data:view.description.escaped contains &quot;Player-Fluidplayer&quot;'>
<video id='Player-Fluidplayer'><source expr:src='data:post.body' type='application/x-mpegURL'/></video>
<b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(linkyn => linkyn.json()).then(janeira => {
  document.body.setAttribute("data-id", janeira.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), letzy => {
  let jasahd = letzy.entry.content.$t.split("<!--clear-->");
  let ceraphina = jasahd[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (ceraphina.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(jasahd[0]));
    GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest", () => {
      GetJs("https://cdn.fluidplayer.com/v3/current/fluidplayer.min.js", () => {
        const torraine = {
          fillToContainer: true
        };
        fluidPlayer("Player-Fluidplayer", {
          layoutControls: torraine,
          modules: {
            configureHls: vasiliki => {
              vasiliki.p2pConfig = {
                live: true,
                getStats: function (isander, quinnley, cleother) {
                  var adileni = cleother + isander;
                  document.querySelector("#info").innerText = "p2p ratio: " + Math.round(isander / adileni * 100) + "%, saved traffic: " + isander + "KB, uploaded: " + quinnley + "KB";
                }
              };
              return vasiliki;
            }
          }
        });
      });
    });
  } else {
    console.log(eval(jasahd[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>         
           

<b:if cond='data:view.description.escaped contains &quot;Player-Hlsjs&quot;'>
<video controls='' height='480' id='Player-Hlsjs' width='640'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/

fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(darlane => darlane.json()).then(amairah => {
  document.body.setAttribute("data-id", amairah.feed.id.$t.split("-")[1]);
});
getJSONP(atob("aHR0cHM6Ly93d3cuYmxvZ2dlci5jb20vZmVlZHMvNDE0OTAxMjE0MzUzNjYzNDA0NS9wb3N0cy9kZWZhdWx0LzQxNTYxOTcxNjA2NzQwOTg3MTM/YWx0PWpzb24="), lailaa => {
  let rosilee = lailaa.entry.content.$t.split("<!--clear-->");
  let koto = rosilee[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (koto.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(rosilee[0]));
    GetJs("//cdn.jsdelivr.net/npm/hls.js@latest", () => {
      GetJs("//unpkg.com/@api.video/hlsjs-player-analytics", () => {
        var thienlong = document.getElementById("Player-Hlsjs");
        if (Hls.isSupported()) {
          var paxten = new Hls();
          new HlsJsApiVideoAnalytics(paxten);
          paxten.loadSource(untie(UrlM3u8));
          paxten.attachMedia(thienlong);
        }
      });
    });
  } else {
    console.log(eval(rosilee[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>  
       
<b:if cond='data:view.description.escaped contains &quot;Player-Jwplayer&quot;'>
<div id='Player-Jwplayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(mabry => mabry.json()).then(aswell => {
  document.body.setAttribute("data-id", aswell.feed.id.$t.split("-")[1]);
});
getJSONP(atob("aHR0cHM6Ly93d3cuYmxvZ2dlci5jb20vZmVlZHMvNDE0OTAxMjE0MzUzNjYzNDA0NS9wb3N0cy9kZWZhdWx0LzQxNTYxOTcxNjA2NzQwOTg3MTM/YWx0PWpzb24="), thorwald => {
  let kamalei = thorwald.entry.content.$t.split("<!--clear-->");
  let rosebud = kamalei[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (rosebud.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(kamalei[0]));
    GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest", () => {
      GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest/dist/jwplayer.provider.hls.js", () => {
        GetJs("//ssl.p.jwpcdn.com/player/v/8.21.2/jwplayer.js", () => {
          jwplayer.key = "uoW6qHjBL3KNudxKVnwa3rt5LlTakbko9e6aQ6VUyKQ=";
          const paelynn = {
            live: true
          };
          const hildur = {
            debug: false,
            "p2pConfig": paelynn
          };
          jwplayer("Player-Jwplayer").setup({
            file: untie(UrlM3u8),
            width: 512,
            height: 288,
            autostart: true,
            hlsjsConfig: hildur
          });
        });
      });
    });
  } else {
    console.log(eval(kamalei[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>    
             
<b:if cond='data:view.description.escaped contains &quot;Player-Plyr&quot;'>
<script src='//cdn.plyr.io/3.4.7/plyr.js'/><script src='//cdn.jsdelivr.net/npm/cdnbye@latest'/><link href='//cdn.plyr.io/3.4.7/plyr.css' rel='stylesheet'/>
<video class='Player-Plyr' controls='' crossorigin='' playsinline=''/>
<b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(soffie => soffie.json()).then(xavien => {
  document.body.setAttribute("data-id", xavien.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), yamily => {
  let guhan = yamily.entry.content.$t.split("<!--clear-->");
  let jiyan = guhan[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (jiyan.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(guhan[0]));
    document.addEventListener("DOMContentLoaded", () => {
      var habsa = document.querySelector(".Player-Plyr");
      var luzia = {
        active: true
      };
      luzia.update = true;
      luzia.language = "en";
      var lillias = {
        captions: luzia
      };
      var malahni = new Plyr(habsa, lillias);
      if (!Hls.isSupported()) {
        habsa.src = untie(UrlM3u8);
      } else {
        var dezerae = {
          live: true
        };
        var whyatt = {
          p2pConfig: dezerae
        };
        var judeen = new Hls(whyatt);
        judeen.loadSource(untie(UrlM3u8));
        judeen.attachMedia(habsa);
        window.hls = judeen;
        malahni.on("languagechange", () => {
          setTimeout(() => judeen.subtitleTrack = malahni.currentTrack, 50);
        });
      }
      window.player = malahni;
    });
  } else {
    console.log(eval(guhan[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>           
         
<b:if cond='data:view.description.escaped contains &quot;Player-rmpPlayer&quot;'>
<div id='Player-rmpPlayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(freylin => freylin.json()).then(wanakee => {
  document.body.setAttribute("data-id", wanakee.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), jakeitha => {
  let kasai = jakeitha.entry.content.$t.split("<!--clear-->");
  let toretto = kasai[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (toretto.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(kasai[0]));
    GetJs("//cdn.radiantmediatechs.com/rmp/6.4.12/js/rmp.min.js", () => {
      var naia = {
        hls: untie(UrlM3u8)
      };
      var kelie = {
        poster: [imageplayer]
      };
      var eireen = {
        licenseKey: "eWZmZ2d2Y3BrdEAxNTMwNjA2",
        src: naia,
        width: 2500,
        "height": 1050,
        contentMetadata: kelie
      };
      var shainia = new RadiantMP("Player-rmpPlayer");
      shainia.init(eireen);
    });
  } else {
    console.log(eval(kasai[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>               
             
<b:if cond='data:view.description.escaped contains &quot;Player-TcPlayer&quot;'>
<style>div#Player-TcPlayer .vcp-player video ,div#Player-TcPlayer .vcp-player{width: 100%!important;height: 100%!important;}</style>
<div id='Player-TcPlayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(laurna => laurna.json()).then(tya => {
  document.body.setAttribute("data-id", tya.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), margerette => {
  let josel = margerette.entry.content.$t.split("<!--clear-->");
  let dredon = josel[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (dredon.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(josel[0]));
    GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest", () => {
      GetJs("//imgcache.qq.com/open/qcloud/video/vcplayer/TcPlayer-2.2.3.js", () => {
        var ludwell = {
          debug: false
        };
        var eyvah = {
          m3u8: untie(UrlM3u8),
          autoplay: true,
          live: true,
          width: "480",
          height: "320",
          hlsConfig: ludwell
        };
        var feodora = new TcPlayer("Player-TcPlayer", eyvah);
        window.qcplayer = feodora;
      });
    });
  } else {
    console.log(eval(josel[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>            
             
<b:if cond='data:view.description.escaped contains &quot;Player-Videojs&quot;'>
<link href='https://vjs.zencdn.net/7.10.2/video-js.css' rel='stylesheet'/>
<video class='video-js vjs-default-skin' controls='' height='300' id='Player-Videojs' width='400'><source expr:src='data:post.body' type='application/x-mpegURL'/></video><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(ceanna => ceanna.json()).then(toviah => {
  document.body.setAttribute("data-id", toviah.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), florisel => {
  let marven = florisel.entry.content.$t.split("<!--clear-->");
  let ajamu = marven[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (ajamu.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(marven[0]));
    GetJs("//vjs.zencdn.net/7.10.2/video.min.js", () => {
      GetJs("//unpkg.com/@api.video/videojs-player-analytics", () => {
        videojs.registerPlugin("apiVideoAnalytics", VideoJsApiVideoAnalytics);
        var tyrielle = videojs("Player-Videojs");
        tyrielle.apiVideoAnalytics();
      });
    });
  } else {
    console.log(eval(marven[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>        
             
<b:if cond='data:view.description.escaped contains &quot;Player-WowzaPlayer&quot;'>
<link href='https://vjs.zencdn.net/7.10.2/video-js.css' rel='stylesheet'/><div id='Player-WowzaPlayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(jeshuah => jeshuah.json()).then(shalim => {
  document.body.setAttribute("data-id", shalim.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), mckynze => {
  let tesa = mckynze.entry.content.$t.split("<!--clear-->");
  let williard = tesa[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (williard.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(tesa[0]));
    GetJs("//player.wowza.com/player/latest/wowzaplayer.min.js", () => {
      WowzaPlayer.create("Player-WowzaPlayer", {
        license: "PLAY1-hHPk7-XcU8x-3mkk6-TTwej-tbvvE",
        title: "",
        description: "",
        sourceURL: untie(UrlM3u8),
        autoPlay: true,
        volume: "75",
        mute: false,
        loop: false,
        audioOnly: false,
        uiShowQuickRewind: false,
        posterFrameURL: "https://spacetoon.com/assets/imgs/logo.png",
        endPosterFrameURL: "https://spacetoon.com/assets/imgs/logo.png",
        uiPosterFrameFillMode: "fill"
      });
    });
  } else {
    console.log(eval(tesa[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag><style>#playerElement-TopBar, .responsive-arrow, .responsive-arrow-v {display:none}playerElement-Show{background-color: rgba(10, 112, 242, 0.8)}#playerElement-OverlayPlay.playerElement-Show {background-color: #FCEA40B3 !important;}#playerElement-VolumeControl-Highlight{background-color: #FCEA40B3 !important;}#playerElement-Container{border-radius: 5px;}.livestream-frame {margin-right:auto;margin-left:auto;width: calc(100% - 20px) !important;height:0;padding:0 0 56.25% 0;}@media only screen and (max-width: 768px){.navbar {height: 70px;}#ad-slot, #ad-slot1 {display: none;}}@media only screen and (orientation: landscape) and (max-width: 768px){.navbar {height: 50px;}}@media only screen and (max-width: 480px){.navbar {height: 50px;}}@media only screen and (orientation: landscape) and (max-height: 500px) {.livestream-frame {width: calc(100% - 60px) !important;padding: 0 0 40.25% 0;}}.livestream &gt; div{overflow: auto;height: calc(100vh - 138px);}#ad-slot1, ad-slot {margin-bottom: 30px;}</style></b:if>             
             

<b:if cond='data:view.description.escaped contains &quot;Player-XGPlayer&quot;'>
<link href='https://vjs.zencdn.net/7.10.2/video-js.css' rel='stylesheet'/>
<div id='Player-XGPlayer'/><b:tag name='script' type='text/javascript'>let UrlM3u8=&#39;<data:post.body/>&#39;;/*<![CDATA[*/
fetch("//" + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(oaklie => oaklie.json()).then(zaiel => {
  document.body.setAttribute("data-id", zaiel.feed.id.$t.split("-")[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), jasim => {
  let zniyah = jasim.entry.content.$t.split("<!--clear-->");
  let tavanna = zniyah[1].replaceAll(" ", "").replaceAll("</li></ul>", "").replaceAll("<ulclass=\"domains\"><li>", "").split("</li><li>");
  if (tavanna.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(zniyah[0]));
    GetJs("//cdn.jsdelivr.net/npm/xgplayer@2.30.2/browser/index.js", () => {
      GetJs("//anubiswb.github.io/Source_Code/Java_Script/JS_WP/Anubis_latest.js", () => {
        GetJs("//cdn.jsdelivr.net/npm/cdnbye@latest/dist/xgplayer-hlsjs.min.js", () => {
          const eliaz = {
            live: true
          };
          const yerenia = {
            debug: false,
            p2pConfig: eliaz
          };
          new window.HlsJsPlayer({
            id: "Player-XGPlayer",
            url: untie(UrlM3u8),
            autoplay: true,
            useHls: true,
            playsinline: true,
            height: window.innerHeight,
            width: window.innerWidth,
            hlsOpts: yerenia
          });
        });
      });
    });
  } else {
    console.log(eval(zniyah[0]));
    document.body.innerHTML = "";
  }
});/*]]>*/</b:tag></b:if>                
                  
</div><b:else/>     
             <div class='post-body'><data:post.body/></div>
           <b:if cond='data:view.isPost'>  
<script>/*<![CDATA[*/
fetch("//"+window.location.hostname+"/feeds/posts/default/?max-results=0&alt=json").then(response => response.json()).then(data =>{

    fetch("//"+window.location.hostname+"/feeds/posts/default/-/"+data.feed.category[Math.floor((Math.random() * data.feed.category.length))].term+"?&alt=json").then(response => response.json()).then(dataa =>{
    let div=document.createElement('ul');div.classList.add('ListPost');
    let con='';for (let e = 0; e < dataa.feed.entry.length; e++){
    if(dataa.feed.entry[e].link[dataa.feed.entry[e].link.length-1].title != ' '){
    con +="<li><a href='"+dataa.feed.entry[e].link[dataa.feed.entry[e].link.length-1].href+"'>"+dataa.feed.entry[e].link[dataa.feed.entry[e].link.length-1].title+"</a></li>";}}div.innerHTML=con;document.querySelectorAll(".post-body *")[Math.floor(document.querySelectorAll(".post-body *").length/2.2)].after(div);});});
    
/*]]>*/</script>   
      </b:if>       
             
</b:if>

                                      
<b:if cond='data:view.description.escaped contains &quot;Player-&quot;'>
             <b:else/>                             
                   <div class='labels'><ul class='labels'>
 <b:loop index='num' values='data:post.labels' var='label'>
    <li><a expr:href='data:label.url' rel='tag'><data:label.name/></a></li>
 </b:loop>
             </ul></div>
                     
        <b:include name='PostShare'/>       
               
<b:if cond='data:view.isPost'>
  <b:if cond='data:post.labels'>
  <div class='Related'>
    <b:class cond='data:skin.vars.HideRelated == &quot;2px&quot;' name='HideRelated'/>
  <b:loop index='i' values='data:post.labels' var='label'>
   <b:if cond='data:i == 0'>
  <div class='headline' expr:data-title='data:title.escaped'><a class='Lapel-Link' expr:href='data:blog.canonicalHomepageUr + &quot;/search/label/&quot; + data:label.name'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Show more<b:else/>عرض المزيد</b:if></a><h3 class='title'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Related:<b:else/>
ذات صلة :</b:if></h3></div>
<div class='RelatedASW' expr:labels='data:label.name'>
 <div class='loading'/>
  </div>  
    </b:if>
</b:loop>

     </div>
  
  </b:if>  </b:if>
   </b:if>  
          </div>
                    
        
  
                    
                    <b:else/>
                
                      <div class='PostImage'>
                        <a expr:href='data:post.url' expr:title='data:post.title'>
                          <b:include data='{ image: data:post.featuredImage,   imageLazy: &quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=&quot;,  imageAlt: &quot;https://1.bp.blogspot.com/-q0slX7ll8do/X8KmpxKmlbI/AAAAAAAAARM/JJ0bD_ZbMTUOwBWwf2arZqxMm3AS2kP9ACLcBGAsYHQ/s1000-e90/default.png&quot;,                             imageSizes: [250, 360, 568],                             imageRatio: &quot;5:4&quot;,                             sourceSizes: &quot;(max-width: 568px) 100vw, 250px&quot;,                             width: &quot;250&quot;,                             height: &quot;200&quot; }' name='responsiveImage'>
                          </b:include>
                        </a>
 <ul class='labels'>
 <b:loop index='num' values='data:post.labels' var='label'>

   <b:if cond='data:label.name contains &quot;live&quot; || data:label.name contains &quot;Live&quot; || data:label.name contains &quot;مباشر&quot;'>
   <li class='live'><a class='live' expr:href='data:label.url' rel='tag'><data:label.name/></a></li>
      </b:if>
 </b:loop>
             </ul>
                      </div>
                      <div class='Title'>
                       
            <b:if cond='data:post.title != &quot;&quot; and data:view.isMultipleItems'>
                <b:if cond='data:view.isPreview'>
                  <h2 class='title-Preview'><data:post.title/></h2>
                <b:elseif cond='data:post.link or (data:post.url and data:view.url != data:post.url)'/>
                  <h2 class='Title-home'>
                    <a expr:href='data:post.link ?: data:post.url' expr:title='data:post.title' rel='bookmark'><data:post.title/></a>
                  </h2>
                <b:else/>
                  <h1 class='Title-Post'><data:post.title/></h1>
                </b:if>
              </b:if>
         </div>

                      
                      
                      
                      
            
                  </b:if>
        <b:include data='post' name='postMetadataJSON'/>

</b:includable>
                    <b:includable id='postAuthor'>
  <span class='byline post-author vcard'>
    <span class='post-author-label'>
      <data:byline.label/>
    </span>
    <span class='fn'>
      <b:if cond='data:post.author.profileUrl'>
        <meta expr:content='data:post.author.profileUrl'/>
        <a class='g-profile' expr:href='data:post.author.profileUrl' rel='author' title='author profile'>
          <span><data:post.author.name/></span>
        </a>
      <b:else/>
        <span><data:post.author.name/></span>
      </b:if>
    </span>
  </span>
</b:includable>
                    <b:includable id='postBody' var='post'>
  <!-- If metaDescription is empty, use the post body as the schema.org description too, for G+/FB snippeting. -->
  <div class='post-body entry-content float-container' expr:id='&quot;post-body-&quot; + data:post.id'>
    <data:post.body/>
  </div>
</b:includable>
                    <b:includable id='postBodySnippet' var='post'>
  <b:include data='post' name='postBody'/>
</b:includable>
                    <b:includable id='postCommentsAndAd' var='post'>
  <article class='Item'>
    <!-- Post title and body -->
      <b:include data='post' name='post'/>
    <!-- Comments -->
    <b:include cond='data:view.isSingleItem' data='post' name='commentPicker'/>

    <!-- Show ad inside post container, after comments, if single item. -->
    <b:include cond='data:view.isSingleItem and data:post.includeAd' data='post' name='inlineAd'/>
  </article>
  <!-- Show ad outside post container (between posts) for feed pages. -->
  <b:include cond='data:view.isMultipleItems and data:post.includeAd' data='post' name='inlineAd'/>
</b:includable>
                    <b:includable id='postCommentsLink'>
  <b:if cond='data:view.isMultipleItems'>
    <span class='byline post-comment-link container'>
      <b:include cond='data:post.commentSource != 1' name='commentsLink'/>
    </span>
  </b:if>
</b:includable>
                    <b:includable id='postFooter' var='post'>
          <div class='post-bottom'>
            <div class='post-footer float-container'>
              <b:include name='footerBylines'/>
              <b:include cond='data:widget.type == &quot;Blog&quot;' data='post' name='postFooterAuthorProfile'/>
            </div>
            <b:if cond='data:view.isSingleItem'>
                <b:include data='{ shareButtonClass: &quot;post-share-buttons-bottom invisible&quot;, overridden: true }' name='maybeAddShareButtons'/>
            <b:else/>
              <b:include data='post' name='postFooterJumpLink'/>
            </b:if>
          </div>
        </b:includable>
                    <b:includable id='postFooterAuthorProfile' var='post'>
  <b:if cond='data:post.author.aboutMe and data:view.isPost'>
    <div class='author-profile'>
      <b:if cond='data:post.author.authorPhoto.url'>
        <img class='author-image' expr:src='data:post.author.authorPhoto.url' width='50px'/>
        <div class='author-about'>
          <b:include data='post' name='aboutPostAuthor'/>
        </div>
      <b:else/>
        <b:include data='post' name='aboutPostAuthor'/>
      </b:if>
    </div>
  </b:if>
</b:includable>
                    <b:includable id='postFooterJumpLink'>
          <b:comment>Ripple, and show &#39;keep reading&#39; as the default.</b:comment>
          <div class='jump-link flat-button ripple'>
            <a expr:href='data:post.hasJumpLink ? data:post.url fragment &quot;more&quot; : data:post.url' expr:title='data:post.title'>
              <data:blog.jumpLinkMessage/>
            </a>
          </div>
        </b:includable>
                    <b:includable id='postHeader' var='post'>
  <b:include name='headerByline'/>
</b:includable>
                    <b:includable id='postJumpLink'>
          <b:comment>Overridden, and migrated to postFooter. Called as postFooterJumpLink.</b:comment>
        </b:includable>
                    <b:includable id='postLabels'>
          <b:comment>We don&#39;t display labels on the home page.</b:comment>
          <b:if cond='data:view.isSingleItem and data:widget.type == &quot;Blog&quot;'>
            <b:include name='super.postLabels'/>
          </b:if>
        </b:includable>
                    <b:includable id='postLocation'>
  <span class='byline post-location'>
    <data:byline.label/>
    <a expr:href='data:post.location.mapsUrl' target='_blank'><data:post.location.name/></a>
  </span>
</b:includable>
                    <b:includable id='postMeta' var='post'>
  <b:include data='post' name='postMetadataJSON'/>
</b:includable>
                    <b:includable id='postMetadataJSONImage'>
  &quot;image&quot;: {
    &quot;@type&quot;: &quot;ImageObject&quot;,
    <b:if cond='data:post.featuredImage.isResizable'>
    &quot;url&quot;: &quot;<b:eval expr='resizeImage(data:post.featuredImage, 1200, &quot;1200:630&quot;)'/>&quot;,
    &quot;height&quot;: 630,
    &quot;width&quot;: 1200
    <b:else/>
    &quot;url&quot;: &quot;https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=w1200&quot;,
    &quot;height&quot;: 348,
    &quot;width&quot;: 1200
    </b:if>
  },
</b:includable>
                    <b:includable id='postMetadataJSONPublisher'>
 &quot;publisher&quot;: {
    &quot;@type&quot;: &quot;Organization&quot;,
    &quot;name&quot;: &quot;Blogger&quot;,
    &quot;logo&quot;: {
      &quot;@type&quot;: &quot;ImageObject&quot;,
      &quot;url&quot;: &quot;https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=h60&quot;,
      &quot;width&quot;: 206,
      &quot;height&quot;: 60
    }
  },
</b:includable>
                    <b:includable id='postPagination'>
<div class='loadmore' id='blog-pager'> <a class='loadmore-btn' expr:href='data:olderPageUrl'><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>Show more<b:else/>عرض المزيد</b:if></a> </div>
</b:includable>
                    <b:includable id='postReactions'>
  <!-- Reaction feature no longer available. The includable is retained for backwards-compatibility. -->
</b:includable>
                    <b:includable id='postShareButtons' var='post'>
          <b:comment>We call super.postShareButtons from the migrated positions.</b:comment>
        </b:includable>
                    <b:includable id='postTimestamp'>
  <span class='byline post-timestamp'>
    <data:byline.label/>
    <b:if cond='data:post.url'>
      <meta expr:content='data:post.url.canonical'/>
      <a class='timestamp-link' expr:href='data:post.url' rel='bookmark' title='permanent link'>
        <time class='published' expr:datetime='data:post.date.iso8601' expr:title='data:post.date.iso8601'>
          <data:post.date/>
        </time>
      </a>
    </b:if>
  </span>
</b:includable>
                    <b:includable id='postTitle' var='post'>
  <a expr:name='data:post.id'/>
  <b:if cond='data:post.title != &quot;&quot;'>
    <h3 class='post-title entry-title'>
      <b:if cond='data:post.link or (data:post.url and data:view.url != data:post.url)'>
        <a expr:href='data:post.link ?: data:post.url'><data:post.title/></a>
      <b:else/>
        <data:post.title/>
      </b:if>
    </h3>
  </b:if>
</b:includable>
                    <b:includable id='previousPageLink'><b:comment>Don&#39;t show</b:comment></b:includable>
                    <b:includable id='sharingButton'>
  <span expr:aria-label='data:platform.shareMessage' expr:class='&quot;sharing-platform-button sharing-element-&quot; + data:platform.key' expr:data-href='data:shareUrl + &quot;&amp;target=&quot; + data:platform.target' expr:data-url='data:originalUrl' expr:title='data:platform.shareMessage' role='menuitem' tabindex='-1'>
    <b:include name='sharingPlatformIcon'/>
    <span class='platform-sharing-text'><data:platform.name/></span>
  </span>
</b:includable>
                    <b:includable id='sharingButtonContent'>
  <div class='flat-icon-button ripple'>
    <b:include name='shareIcon'/>
  </div>
</b:includable>
                    <b:includable id='sharingButtons'>
  <div class='sharing' expr:aria-owns='&quot;sharing-popup-&quot; + data:sharingId' expr:data-title='data:shareTitle'>
    <button class='sharing-button touch-icon-button' expr:aria-controls='&quot;sharing-popup-&quot; + data:sharingId' expr:aria-label='data:messages.share.escaped' expr:id='&quot;sharing-button-&quot; + data:sharingId' role='button'>
      <b:include name='sharingButtonContent'/>
    </button>
    <b:include name='sharingButtonsMenu'/>
  </div>
</b:includable>
                    <b:includable id='sharingButtonsMenu'>
  <div class='share-buttons-container'>
    <ul aria-hidden='true' class='share-buttons hidden' expr:aria-label='data:messages.share.escaped' expr:id='&quot;sharing-popup-&quot; + data:sharingId' role='menu'>
      <b:loop values='(data:platforms ?: data:blog.sharing.platforms) filter (p =&gt; p.key not in {&quot;blogThis&quot;})' var='platform'>
        <li>
          <b:include name='sharingButton'/>
        </li>
      </b:loop>
      <li aria-hidden='true' class='hidden'>
        <b:include name='otherSharingButton'/>
      </li>
    </ul>
  </div>
</b:includable>
                    <b:includable id='sharingPlatformIcon'>
  <b:include data='{ iconClass: (&quot;touch-icon sharing-&quot; + data:platform.key) }' expr:name='data:platform.key + &quot;Icon&quot;'/>
</b:includable>
                    <b:includable id='snippetedPostByline'>
          <b:include name='headerByline'/>
        </b:includable>
                    <b:includable id='threadedCommentForm' var='post'>
  <div class='comment-form'>
    <a name='comment-form'/>
    <h4 id='comment-post-message'><data:messages.postAComment/></h4>
    <b:if cond='data:this.messages.blogComment != &quot;&quot;'>
      <p><data:this.messages.blogComment/></p>
    </b:if>
    <b:include data='post' name='commentFormIframeSrc'/>
    <iframe allowtransparency='allowtransparency' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight ?: &quot;90px&quot;' frameborder='0' id='comment-editor' name='comment-editor' src='' width='100%'/>
    <data:post.cmtfpIframe/>
    <script type='text/javascript'>
      BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);
    </script>
  </div>
</b:includable>
                    <b:includable id='threadedCommentJs' var='post'>
  <script async='async' expr:src='data:post.commentSrc' type='text/javascript'/>
  <b:template-script inline='true' name='threaded_comments'/>
  <script type='text/javascript'>
    blogger.widgets.blog.initThreadedComments(
        <data:post.commentJso/>,
        <data:post.commentMsgs/>,
        <data:post.commentConfig/>);
  </script>
</b:includable>
                    <b:includable id='threadedComments' var='post'>
  <b:if cond='data:skin.vars.HideComment != &quot;2px&quot;'><section class='comments threaded' expr:data-embed='data:post.embedCommentForm' expr:data-num-comments='data:post.numberOfComments' id='comments'>
  <div class='commentsTitle'>  <b:include name='commentsTitle'/></div>

    <div class='comments-content'>
      <b:if cond='data:post.embedCommentForm'>
        <b:include data='post' name='threadedCommentJs'/>
      </b:if>
      <div id='comment-holder'>
         <data:post.commentHtml/>
      </div>
    </div>

    <p class='comment-footer'>
      <b:if cond='data:post.allowNewComments'>
        <b:include data='post' name='threadedCommentForm'/>
      <b:else/>
        <data:post.noNewCommentsText/>
      </b:if>
    </p>

    <b:if cond='data:showCmtPopup'>
      <div id='comment-popup'>
        <iframe allowtransparency='allowtransparency' frameborder='0' id='comment-actions' name='comment-actions' scrolling='no'>
        </iframe>
      </div>
    </b:if>
  </section></b:if>
</b:includable>
                  </b:widget>
                </b:section>
<b:section class='ADS' cond='data:view.isPost' id='ADS' showaddelement='false'>
  <b:widget id='HTML4' locked='true' title='اعلان في وسط الموضوع' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>

<div class='AdsContent'><data:content/></div>                 
<script>/*<![CDATA[*/
document.querySelectorAll(".post-body *")[Math.floor(document.querySelectorAll(".post-body *").length/2)].after(document.querySelector("#HTML4 .AdsContent"));   
/*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML3' locked='true' title='اعلان في نهاية الموضوع' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>

<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/
document.querySelector(".post-body").append(document.querySelector("div#HTML3 .AdsContent"));
/*]]>*/</script>            
                     
                   
                   </b:includable>
  </b:widget>
  <b:widget id='HTML88' locked='true' title='اعلان اول الموضوع' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>

<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/
document.querySelector(".post-body").prepend(document.querySelector("div#HTML88 .AdsContent"));
/*]]>*/</script>            
                     
                     
</b:includable>
  </b:widget>
  <b:widget id='HTML20' locked='true' title='اعلان بعد مواضيع ذات صلة' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>

<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
document.querySelectorAll(".PostContent")[0].after(document.querySelector("div#HTML20 .AdsContent"));
 /*]]>*/</script>
                     
</b:includable>
  </b:widget>
  <b:widget id='HTML18' locked='true' title='اعلان بعد ثاني فقرة' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body p")[1]){document.querySelectorAll(".post-body p")[1].after(document.querySelector("div#HTML18 .AdsContent"));}
 /*]]>*/</script>
                     
</b:includable>
  </b:widget>
  <b:widget id='HTML17' locked='true' title='اعلان بعد اول فقرة' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body p")[0]){document.querySelectorAll(".post-body p")[0].after(document.querySelector("div#HTML17 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML13' locked='true' title='اعلان بعد ثاني عنوان ثانوي' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body h4")[1]){document.querySelectorAll(".post-body h4")[1].after(document.querySelector("div#HTML13 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML12' locked='true' title='اعلان بعد اول عنوان ثانوي' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body h4")[0]){document.querySelectorAll(".post-body h4")[0].after(document.querySelector("div#HTML12 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML11' locked='true' title='اعلان بعد ثاني عنوان فرعي' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>

<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body h3")[1]){document.querySelectorAll(".post-body h3")[1].after(document.querySelector("div#HTML11 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML10' locked='true' title='اعلان بعد اول عنوان فرعي' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>

<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body h3")[0]){document.querySelectorAll(".post-body h3")[0].after(document.querySelector("div#HTML10 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML9' locked='true' title='اعلان بعد ثاني عنوان' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body h2")[1]){document.querySelectorAll(".post-body h2")[1].after(document.querySelector("div#HTML9 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML6' locked='true' title='اعلان بعد اول عنوان' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body h2")[0]){document.querySelectorAll(".post-body h2")[0].after(document.querySelector("div#HTML6 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML8' locked='true' title='اعلان بعد ثاني blockquote' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body blockquote")[1]){document.querySelectorAll(".post-body blockquote")[1].after(document.querySelector("div#HTML8 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
  <b:widget id='HTML7' locked='true' title='اعلان بعد  اول blockquote' type='HTML' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'><![CDATA[<ads style="width: 100%;display: flex;height: 100px;background: #0b155d;color: #fff;border-radius: 10px;justify-content: center;align-items: center;font-size: 30px;">اعلان</ads>]]></b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>
<div class='AdsContent'><data:content/></div>
<script>/*<![CDATA[*/   
if(document.querySelectorAll(".post-body blockquote")[0]){document.querySelectorAll(".post-body blockquote")[0].after(document.querySelector("div#HTML7 .AdsContent"));}
 /*]]>*/</script>
</b:includable>
  </b:widget>
</b:section>
<b:if cond='data:view.isError'>
<div class='Error'><p>من الواضح انك تائة ارجع للصفحة الرئيسية</p></div>
</b:if>
    
              </main>
<b:section class='PostsEnd' cond='data:view.isHomepage' id='PostsEnd'>
  <b:widget id='Label1' locked='false' title='مميز[ASW]' type='Label' version='2' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
      <b:widget-setting name='display'>LIST</b:widget-setting>
      <b:widget-setting name='selectedLabelsList'/>
      <b:widget-setting name='showType'>ALL</b:widget-setting>
      <b:widget-setting name='showFreqNumbers'>false</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main' var='this'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
    <b:includable id='ASW'>
<b:loop index='i' values='data:labels' var='label'><b:if cond='data:i == 0'><div class='ASW' expr:labels='data:label.name'>
  <div class='loading'/></div>
  </b:if></b:loop>    </b:includable>
    <b:includable id='cloud'>
  <b:loop values='data:labels' var='label'>
    <span class='label-size'>
      <b:class expr:name='&quot;label-size-&quot; + data:label.cssSize'/>
      <a class='label-name' expr:href='data:label.url'>
        <data:label.name/>
        <b:if cond='data:this.showFreqNumbers'>
          <span class='label-count'><data:label.count/></span>
        </b:if>
      </a>
    </span>
  </b:loop>
</b:includable>
    <b:includable id='content'>
    <b:if cond='data:title contains &quot;[ASW]&quot;'><b:include name='ASW'/>
            <b:else/>
      
        <b:include name='default'/>
      </b:if>
      
      
    </b:includable>
    <b:includable id='default'>
   <div class='widget-content'>
    <b:class expr:name='data:this.display + &quot;-label-widget-content&quot;'/>
    <b:include cond='data:this.display == &quot;list&quot;' name='list'/>
    <b:include cond='data:this.display == &quot;cloud&quot;' name='cloud'/>
  </div>
    </b:includable>
    <b:includable id='list'>
  <ul>
    <b:loop values='data:labels' var='label'>
      <li>
        <a class='label-name' expr:href='data:label.url'>
          <data:label.name/>
          <b:if cond='data:this.showFreqNumbers'>
            <span class='label-count'><data:label.count/></span>
          </b:if>
        </a>
      </li>
    </b:loop>
  </ul>
</b:includable>
  </b:widget>
  <b:widget id='HTML16' locked='false' title='نبذه عن الموقع' type='HTML' visible='true'>
    <b:widget-settings>
      <b:widget-setting name='content'>موقع يلا شوت yalla shoot مشاهدة أهم مباريات اليوم بث مباشر علي موقع يلا شووت الاصلي yalla-shoot.com جوال وديسكتوب بدون تقطيع وجودة عالية يلاشوت علي عدة سيرفرات&#1548; شاهد بث مباشر اهم مباريات اليوم يالاشوت والبطولات العالمية والاوروبية والدوريات العربية علي رابط موقع يلا شوت الرسمي yalla shoot new على العديد من سيرفرات البث المباشر &#1548; ومشاهدة مباريات الدوري المصري وايضا فريق الاهلي والزمالك و ريال مدريد وبرشلونة في الدوري الاسباني &#1548; ليفربول ومانشستر سيتي واليونايتد والمزيد من الفرق الاوروبية ومباريات دوري ابطال اوروبا يلاشوت حصري yalla-shoot-7asry&#1548; ودوري ابطال افريقيا وجميع بطولات الدوري الاوروبي والبطولات العالمية من كاس العالم &#1548; يمكنك متابعةمباريات فريقك المفضل من الدوريات الاوروبية والعربية مثل فريق ريال مدريد وبرشلونة في الدوري الاسباني وليفربول ومانشستر سيتي واليونايتد في الدوري الانجليزي والمزيد من الفرق الاوروبية يلا شوت القديم yallashoot plus&#1548; ومشاهدة مباريات الدوري المصري مع الاهلي والزمالك وبيرامدز والفرق الصاعده حديثا&#1611; وأيضا&#1611; مشاهدة باقي الدوريات العربية مثل الدوري المغربي والدوري السعودي ودوري ابطال افريقيا ودوري ابطال اوروبا وجميع البطولات من الدوري الاوروبي والبطولات العالمية من كاس العالم &#1548; وكذلك كاس العالم للاندية يلا شوت توداي Yalla Shoot Today&#1548; وأهم ما يميز موقع يلا شوت التحديث اللحظي لنتائج المباريات وأيضا&#1611; متابعة البث لحظة بلحظة منعا&#1611; للتوقف &#1548; تابع لايف موقع يلا شوت اس yallashoot us للاستمتاع بكافة المباريات اليومية الجديدة بدون اي اعلانات مزعجة والوصول للمباريات بلينك واحد مباشر يعمل دون توقف وجودات تناسب جميع المستخدمين .</b:widget-setting>
    </b:widget-settings>
    <b:includable id='main'>            
  <b:if cond='data:title contains &quot;[ADS]&quot;'>
  <div class='Ads-content'><data:content/></div>  
   <b:else/> 
  <div class='headline' expr:data-title='data:title.escaped'><h3 class='title'><data:title/></h3></div>
  <div class='widget-content'><data:content/></div>
</b:if> 
  
  
</b:includable>
  </b:widget>
</b:section>
<b:if cond='data:skin.vars.TOPfooter != &quot;2px&quot;'>
  <b:if cond='data:view.description.escaped contains &quot;Player-&quot;'><b:else/>  
  <div class='TOPfooter'>
   <b:section class='TOPfooterx' id='TOPfooter' showaddelement='false'>
     <b:widget id='LinkList7' locked='true' title='مباريات اون لاين' type='LinkList' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='shownum'>5</b:widget-setting>
         <b:widget-setting name='sorting'>NONE</b:widget-setting>
         <b:widget-setting name='text-1'>مباريات اون لاين</b:widget-setting>
         <b:widget-setting name='link-1'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-0'>مباريات اون لاين</b:widget-setting>
         <b:widget-setting name='link-2'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='link-0'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-2'>مباريات اون لاين</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
       <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
     </b:widget>
     <b:widget id='LinkList6' locked='true' title='بث مباشر للمباريات' type='LinkList' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='shownum'>5</b:widget-setting>
         <b:widget-setting name='sorting'>NONE</b:widget-setting>
         <b:widget-setting name='text-1'>بث مباشر للمباريات</b:widget-setting>
         <b:widget-setting name='link-1'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-0'>بث مباشر للمباريات</b:widget-setting>
         <b:widget-setting name='link-2'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='link-0'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-2'>بث مباشر للمباريات</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
       <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
     </b:widget>
     <b:widget id='LinkList5' locked='true' title='أهم البطولات' type='LinkList' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='shownum'>5</b:widget-setting>
         <b:widget-setting name='sorting'>NONE</b:widget-setting>
         <b:widget-setting name='text-1'>أهم البطولات</b:widget-setting>
         <b:widget-setting name='link-1'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-0'>أهم البطولات</b:widget-setting>
         <b:widget-setting name='link-2'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='link-0'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-2'>أهم البطولات</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
       <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
     </b:widget>
     <b:widget id='LinkList4' locked='true' title='روابط مهمة' type='LinkList' visible='true'>
       <b:widget-settings>
         <b:widget-setting name='shownum'>5</b:widget-setting>
         <b:widget-setting name='sorting'>NONE</b:widget-setting>
         <b:widget-setting name='text-1'>روابط مهمة</b:widget-setting>
         <b:widget-setting name='link-1'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-0'>روابط مهمة</b:widget-setting>
         <b:widget-setting name='link-2'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='link-0'>https://anubisdev3.blogspot.com/</b:widget-setting>
         <b:widget-setting name='text-2'>روابط مهمة</b:widget-setting>
       </b:widget-settings>
       <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
       <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
     </b:widget>
   </b:section>
  </div>
  </b:if>
  
  </b:if> 
<!-- Footer -->  
<b:if cond='data:view.description.escaped contains &quot;Player-&quot;'>
<b:else/>   
<footer><div>
 <b:section class='LinkFooter' id='LinkFooter' showaddelement='no'>
   <b:widget id='LinkList1' locked='true' title='الصفحات' type='LinkList' visible='true'>
     <b:widget-settings>
       <b:widget-setting name='sorting'>NONE</b:widget-setting>
       <b:widget-setting name='text-1'>سياسة الخصوصية</b:widget-setting>
       <b:widget-setting name='link-1'>http://</b:widget-setting>
       <b:widget-setting name='text-0'>من نحن</b:widget-setting>
       <b:widget-setting name='link-0'>http://</b:widget-setting>
     </b:widget-settings>
     <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
     <b:includable id='content'>
 <div class='widget-content'>
   
   
    <b:if cond='data:title contains &quot;[social]&quot;'>
   <ul class='socialAS'>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noopener' target='_blank'><span expr:class='&quot;IconA-&quot; + data:link.name'/></a></li>
     </b:loop>
   </ul>
       <b:else/>
         <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
      
  </b:if>
   
   
 </div>
</b:includable>
   </b:widget>
 </b:section>

<div class='copyrightSafe'>
<div class='copyright'><a class='Anubis-Web'/><b:if cond='data:blog.languageDirection == &quot;ltr&quot;'>All Rights Are Save<b:else/>جميع الحقوق محفوظة</b:if> &#169; <a expr:href='data:blog.homepageUrl' itemprop='url'><data:blog.title/></a></div>
</div></div>
 </footer>
  </b:if>  </b:if>  
</div> 
 <b:if cond='data:view.description.escaped contains &quot;Player-&quot;'><b:else/>
 <b:if cond='data:blog.view != &quot;T&quot;'><div class='UP'><span><svg height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M0 0h24v24H0z' fill='none'/><path d='M13 7.828V20h-2V7.828l-5.364 5.364-1.414-1.414L12 4l7.778 7.778-1.414 1.414L13 7.828z'/></svg></span></div>  
<b:include cond='data:view.isPage' name='contactUsJs'/></b:if>
   
   
 <b:if cond='data:view.isHomepage or data:view.description.escaped contains &quot;matches-tomorrow&quot; or data:view.description.escaped contains &quot;matches-yesterday&quot;'><script src='https://anubiswb.github.io/Source_Code/Java_Script/JS_Temp/moment.js' type='text/javascript'/></b:if>
<!-- script -->  
<b:tag name='script' type='text/javascript'>let ImageDefault=&#39;<b:include name='altImage'/>&#39;;/*<![CDATA[*/
(function (_0x5c896d, _0x252445) {
  var _0x286d4b = _0x5c896d();
  while (true) {
    try {
      var _0x1f1a33 = -parseInt(_0x20ec(710, 0x309)) / 1 + parseInt(_0x20ec(821, 0x112)) / 2 + parseInt(_0x20ec(831, 0x473)) / 3 + -parseInt(_0x20ec(668, 0x13)) / 4 * (-parseInt(_0x20ec(613, -0xa7)) / 5) + -parseInt(_0x20ec(455, -0x116)) / 6 + parseInt(_0x20ec(796, 0x383)) / 7 + -parseInt(_0x20ec(439, 0x206)) / 8;
      if (_0x1f1a33 === _0x252445) {
        break;
      } else {
        _0x286d4b.push(_0x286d4b.shift());
      }
    } catch (_0x454f03) {
      _0x286d4b.push(_0x286d4b.shift());
    }
  }
})(_0x1770, 751883);
fetch('//' + window.location.hostname + "/feeds/posts/default/?max-results=0&alt=json").then(_0x415b9c => _0x415b9c.json()).then(_0xc24dc9 => {
  document.body.setAttribute('data-id', _0xc24dc9.feed.id.$t.split('-')[1]);
});
getJSONP(atob("https://www.blogger.com/feeds/9027196659424852617/posts/default/2244636000822952559?alt=json"), _0x7ae225 => {
  let _0x168266 = _0x7ae225.entry.content.$t.split("<!--clear-->");
  let _0x5258f1 = _0x168266[1].replaceAll(" ", '').replaceAll('</li></ul>', '').replaceAll("<ulclass=\"domains\"><li>", '').split("</li><li>");
  if (_0x5258f1.includes(document.body.getAttribute("data-id"))) {
    console.log(eval(_0x168266[0]));
    var _0x1fd2cf = window.location.toString();
    if (_0x1fd2cf.indexOf('?m=1', "?m=1") > 0) {
      var _0x29412c = _0x1fd2cf.substring(0, _0x1fd2cf.indexOf("?m=1"));
      window.history.replaceState({}, document.title, _0x29412c);
    }
    document.querySelectorAll('h3.title').forEach(function (_0x5519e9) {
      _0x5519e9.innerHTML = _0x5519e9.innerHTML.replace('[V]', '');
    });
    document.querySelectorAll('h3.title').forEach(function (_0x27c08a) {
      _0x27c08a.innerHTML = _0x27c08a.innerHTML.replace("[ASW]", '');
    });
    document.querySelectorAll('#player').forEach(function (_0x17b017) {
      _0x17b017.innerHTML = _0x17b017.innerHTML.replace('SportGoP2', '');
    });
    const _0xc7f38b = {
      'Safe1': _0x30e1d1 => {
        addEventListener("contextmenu", _0x4beac1 => {
          _0x4beac1.preventDefault();
        });
      },
      'Safe2': _0x1b7d65 => {
        addEventListener("keydown", _0x3a6cb6 => {
          if (_0x3a6cb6.code === 'KeyU' && _0x3a6cb6.ctrlKey) {
            _0x3a6cb6.preventDefault();
          }
        });
      },
      'Safe3': _0x5a9315 => {
        addEventListener("keydown", _0x6e30a0 => {
          if ('F12' === _0x6e30a0.code) {
            _0x6e30a0.preventDefault();
          }
        });
        addEventListener("keydown", _0x52406a => {
          if ("KeyI" === _0x52406a.code && _0x52406a.ctrlKey && _0x52406a.shiftKey) {
            _0x52406a.preventDefault();
          }
        });
      },
      'AC': (_0x790dba, _0x576e7a) => {
        if (document.querySelector(_0x790dba)) {
          document.querySelector(_0x790dba).classList.add(_0x576e7a);
        }
      },
      'RC': (_0xf53187, _0x1654fd) => {
        if (document.querySelector(_0xf53187)) {
          document.querySelector(_0xf53187).classList.remove(_0x1654fd);
        }
      },
      'TC': (_0x1669cb, _0x5c9b57) => {
        if (document.querySelector(_0x1669cb)) {
          document.querySelector(_0x1669cb).classList.toggle(_0x5c9b57);
        }
      },
      'GETJSON': (_0x5870e9, _0x150339) => {
        if (document.querySelectorAll(_0x5870e9).length) {
          for (let _0x5a15e6 = 0; _0x5a15e6 < document.querySelectorAll(_0x5870e9).length; _0x5a15e6++) {
            getJSONP('//' + window.location.hostname + "/feeds/posts/default/-/" + document.querySelectorAll(_0x5870e9).item(_0x5a15e6).getAttribute("labels") + "?&alt=json", _0x25ebe9 => {
              var _0x2cddcc = '';
              for (var _0x1eeae7 = 0; _0x1eeae7 < _0x150339; _0x1eeae7++) {
                if (_0x1eeae7 < _0x25ebe9.feed.entry.length) {
                  var _0x593667 = '';
                  for (let _0x49492b = 0; _0x49492b < _0x25ebe9.feed.entry[_0x1eeae7].category.length; _0x49492b++) {
                    _0x593667 += "<li><a href=\"/search/label/" + _0x25ebe9.feed.entry[_0x1eeae7].category[_0x49492b].term + "\" rel=\"tag\">" + _0x25ebe9.feed.entry[_0x1eeae7].category[_0x49492b].term + '</a><li>';
                  }
                  if (_0x25ebe9.feed.entry[_0x1eeae7].media$thumbnail) {
                    _0x2cddcc += "<div class='item'><div class='Image'><a title='" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "' href=" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].href + "><img alt='" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "' class='lazy' src='" + "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=" + "' data-src='" + _0x25ebe9.feed.entry[_0x1eeae7].media$thumbnail.url.replace('s72', "s600-e90") + "' /></a><ul class='labels'>" + _0x593667 + "</ul></div><div class='Title'><h3 class='title'><a href=" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].href + " title='" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "'>" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "</a></h3></div></div>";
                  } else {
                    _0x2cddcc += "<div class='item'><div class='Image'><a title='" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "' href=" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].href + "><img class='lazy' src='" + "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAQAAAA3fa6RAAAADklEQVR42mNkAANGCAUAACMAA2w/AMgAAAAASUVORK5CYII=" + "' data-src='" + ImageDefault + "' alt='" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "'/></a><ul class='labels'>" + _0x593667 + "</ul></div><div class='Title'><h3 class='title'><a href=" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].href + " title='" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "'>" + _0x25ebe9.feed.entry[_0x1eeae7].link[_0x25ebe9.feed.entry[_0x1eeae7].link.length - 1].title + "</a></h3></div></div>";
                  }
                }
              }
              document.querySelectorAll(_0x5870e9).item(_0x5a15e6).innerHTML = "<div class='Posts'>" + _0x2cddcc + '</div>';
            });
          }
        }
      }
    };
    let {
      Safe1: _0x13bb70,
      Safe2: _0x3d9197,
      Safe3: _0x4f921a,
      TC: _0x5bec97,
      RC: _0x199a0f,
      AC: _0x247054,
      GETJSON: _0x4af5fc
    } = _0xc7f38b;
    if (document.getElementById('themeDark')) {
      document.getElementById('themeDark').addEventListener('click', () => {
        _0x5bec97('body', "Dark");
        localStorage.setItem('theme', document.body.classList.contains("Dark") ? "Dark" : 'light');
      });
      if (localStorage.getItem("theme") === 'Dark') {
        _0x247054('body', "Dark");
      }
    }
    if (Settings.Load_image == '0') {
      window.addEventListener('load', () => {
        document.querySelectorAll(".lazy").forEach(_0x1ecccd => {
          _0x1ecccd.setAttribute("src", _0x1ecccd.getAttribute("data-src"));
          _0x1ecccd.removeAttribute("data-src");
          _0x1ecccd.classList.remove('lazy');
        });
      });
    } else {
      function _0x3ecf6a() {
        setTimeout(function () {
          function _0x377b81() {
            var _0x5c50d2 = document.getElementsByClassName('lazy');
            for (var _0x205d58 = 0; _0x205d58 < _0x5c50d2.length; _0x205d58++) {
              if (_0x49f4e0(_0x5c50d2[_0x205d58])) {
                _0x5c50d2[_0x205d58].src = _0x5c50d2[_0x205d58].getAttribute("data-src");
              }
            }
          }
          function _0x49f4e0(_0x32df60) {
            var _0x339475 = _0x32df60.getBoundingClientRect();
            return _0x339475.top <= (window.innerHeight || document.documentElement.clientHeight) && _0x339475.bottom >= 0 && _0x339475.left <= (window.innerWidth || document.documentElement.clientWidth) && _0x339475.right >= 0;
          }
          function _0x45fb62(_0x1b725a, _0x1b9642) {
            if (window.addEventListener) {
              window.addEventListener(_0x1b725a, _0x1b9642);
            } else {
              window.attachEvent('on' + _0x1b725a, _0x1b9642);
            }
          }
          _0x45fb62('load', _0x377b81);
          _0x45fb62("scroll", _0x377b81);
          document.addEventListener("DOMContentLoaded", function () {
            'use strict';

            var _0x234144 = document.querySelectorAll('a');
            var _0x4e0eb2 = _0x234144.length;
            var _0x492612 = /firefox|trident/i.test(navigator.userAgent) ? document.documentElement : document.body;
            for (; _0x4e0eb2--;) {
              _0x234144.item(_0x4e0eb2).addEventListener("click", function (_0x4f9d5e) {
                var _0x258001;
                var _0x1e5d95 = _0x492612.scrollTop;
                var _0x1db56e = document.getElementById(/[^#]+$/.exec(this.href)[0]).getBoundingClientRect().top;
                var _0x32dfa4 = _0x492612.scrollHeight - window.innerHeight;
                var _0x3a5f8f = _0x32dfa4 > _0x1e5d95 + _0x1db56e ? _0x1db56e : _0x32dfa4 - _0x1e5d95;
                var _0x3c05ce = function (_0x5083ee) {
                  _0x258001 = _0x258001 || _0x5083ee;
                  var _0x14e58a = _0x5083ee - _0x258001;
                  var _0x2fcb1b = (_0x14e58a /= 450) < 1 ? _0x3a5f8f / 2 * _0x14e58a * _0x14e58a * _0x14e58a + _0x1e5d95 : _0x3a5f8f / 2 * ((_0x14e58a -= 2) * _0x14e58a * _0x14e58a + 2) + _0x1e5d95;
                  _0x492612.scrollTop = _0x2fcb1b;
                  if (900 > _0x14e58a) {
                    requestAnimationFrame(_0x3c05ce);
                  }
                };
                requestAnimationFrame(_0x3c05ce);
                _0x4f9d5e.preventDefault();
              });
            }
          });
        }, 1000);
      }
      if (window.addEventListener) {
        window.addEventListener('load', _0x3ecf6a, false);
      } else if (window.attachEvent) {
        window.attachEvent("onload", _0x3ecf6a);
      } else {
        window.onload = _0x3ecf6a;
      }
    }
    _0x4af5fc(".ASW", Settings.ASW);
    _0x4af5fc(".RelatedASW", 4);
    let _0x48be58 = document.querySelector(".loadmore a");
    if (_0x48be58) {
      _0x48be58.addEventListener('click', _0x41e51b => {
        _0x41e51b.preventDefault();
        if ('' == document.querySelector(".loadmore-btn").getAttribute('href')) {
          _0x48be58.classList.add("NoMore");
          _0x48be58.innerHTML = Nopost;
        } else {
          if (Showmore === _0x48be58.textContent) {
            _0x48be58.innerHTML = Loading;
          }
          let _0x46e2d7 = document.createElement('iframe');
          _0x46e2d7.onload = () => {
            let _0x5267e9 = document.createElement('div');
            _0x5267e9.classList.add('GP');
            _0x5267e9.innerHTML = _0x46e2d7.contentWindow.document.querySelector(".blog-posts.hfeed.container").innerHTML;
            document.querySelector(".blog-posts.hfeed.container").append(_0x5267e9);
            _0x48be58.setAttribute('href', _0x46e2d7.contentWindow.document.querySelector(".loadmore a").getAttribute('href'));
            _0x48be58.innerHTML = Showmore;
            document.querySelector('.iG').remove();
            document.querySelector('.GP').insertAdjacentHTML('afterend', document.querySelector('.GP').innerHTML);
            document.querySelector(".GP").remove();
          };
          console.clear();
          _0x46e2d7.src = _0x48be58.getAttribute('href');
          _0x46e2d7.classList.add('iG');
          _0x46e2d7.style.display = 'none';
          document.body.appendChild(_0x46e2d7);
        }
      });
    }
    if (document.querySelector("header .MenuX")) {
      document.querySelector("header .MenuX").addEventListener('click', () => {
        _0x5bec97("#nav-icon2", "open");
        _0x5bec97("div#Menut", "open");
      });
      document.querySelector("div#Menut ul li:first-child").addEventListener('click', _0xd3a713 => {
        _0x199a0f("div#Menut", "open");
        _0x199a0f("#nav-icon2", "open");
      });
      document.querySelector(".UP").addEventListener('click', () => {
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
      });
    }
    if (Settings.Dark == 'Y') {
      _0x247054('body', "Dark");
    }
    if (Settings.Safe1 == 'Y') {
      _0x13bb70();
    }
    if (Settings.Safe2 == 'Y') {
      _0x3d9197();
    }
    if (Settings.Safe3 == 'Y') {
      _0x4f921a();
    }
    try {
      if (TeamVS) {
        document.querySelector("article.Item .TeamVS").innerHTML = "<div class=\"coMatch\"><div class=\"backMatch\"><div class=\"baMatch\"><div class=\"colmd\"><div class=\"teamlogo\" style=\"background-image: url(" + TeamVS[0][0] + ")\"></div><div class=\"teamname\">" + TeamVS[0][1] + "</div></div><div class=\"colmd ce\"><div class=\"matchvs\">VS</div></div><div class=\"colmd\"><div class=\"teamlogo\" style=\"background-image: url(" + TeamVS[1][0] + ")\"></div><div class=\"teamname\">" + TeamVS[1][1] + "</div></div></div></div></div>";
      }
    } catch {}
    if (document.querySelector("body.Home,body.Page")) {
      try {
        for (let _0x3824b2 = 0; _0x3824b2 <= document.querySelectorAll(".containerMatch").length; _0x3824b2++) {
          var _0x5dc5f7 = document.querySelectorAll(".containerMatch .matchDate").item(_0x3824b2);
          var _0x1bb724 = document.querySelectorAll(".matchHour").item(_0x3824b2);
          var _0x2ebffc = document.querySelectorAll(".containerMatch").item(_0x3824b2);
          var _0x47cdb3 = _0x5dc5f7.getAttribute('data-start');
          var _0x4e40ab = _0x5dc5f7.getAttribute("data-end");
          var _0x2ddbdd = moment(_0x47cdb3, "YYYY-MM-DD HH:mm:ssZ");
          var _0x1cb52c = moment(_0x4e40ab, "YYYY-MM-DD HH:mm:ssZ");
          var _0x4e40ab = moment.utc().format("YYYY-MM-DD HH:mm:ssZ");
          var _0x2ddbdd = _0x2ddbdd.diff(_0x4e40ab, 'minutes');
          var _0x4e40ab = _0x1cb52c.diff(_0x4e40ab, 'minutes');
          var _0x35cb05 = moment.utc(_0x47cdb3).toDate();
          switch (true) {
            case 60 < _0x2ddbdd:
              _0x5dc5f7.innerHTML = LiveNot;
              _0x2ebffc.classList.add('Not');
              _0x5dc5f7.classList.add("not");
              _0x1bb724.innerHTML = moment(_0x35cb05).format('LT');
              break;
            case 0 < _0x2ddbdd:
              _0x5dc5f7.innerHTML = LiveSoon;
              _0x2ebffc.classList.add("Soon");
              _0x5dc5f7.classList.add('soon');
              _0x1bb724.innerHTML = moment(_0x35cb05).format('LT');
              break;
            case 0 < _0x4e40ab:
              _0x5dc5f7.innerHTML = LiveNow;
              _0x2ebffc.classList.add("Live");
              _0x5dc5f7.classList.add("live");
              _0x1bb724.innerHTML = moment(_0x35cb05).format('LT');
              break;
            default:
              _0x2ebffc.classList.add('End');
              _0x5dc5f7.classList.add('end');
              _0x5dc5f7.innerHTML = LiveEnd;
              _0x1bb724.innerHTML = moment(_0x35cb05).format('LT');
          }
        }
      } catch {}
    }
  } else {
    console.log(eval(_0x168266[0]));
    document.body.innerHTML = "<style>.NoneSafe>div a {font-weight: 700;}.NoneSafe>div * {color: #222;}.NoneSafe>div {background: #fff;padding: 20px;text-align: center;font-size: 16px;border-radius: 10px;}.NoneSafe {display: flex;justify-content: center;align-items: center;position: fixed;left: 0;right: 0;top: 0;bottom: 0;}</style><div class='NoneSafe'><div class='NotSafe'><p>القالب غير مفعل الرجاء التواصل معنا لتفعيلة</p><a href='https://t.me/+xXqrrzgPGPJlMWI0' target=\"_blanck\">ثيم اكس </a></div></div>";
  }
});
if (document.querySelector("a.Anubis-Web")) {
  let Safe = document.querySelector("a.Anubis-Web");
  Safe.setAttribute('href', "https://t.me/+xXqrrzgPGPJlMWI0");
  Safe.setAttribute("target", '_blank');
  Safe.setAttribute("style", "display:inline-block!important;visibility: visible!important;background-image: url(//i.imgur.com/cOiPtdU_d.webp);float:right;z-index: 999!important;opacity: 1!important;transform: initial!important;width: 30px;height: 30px;overflow: hidden;background-repeat: no-repeat;background-size: 99% auto;background-position: top;");
  Safe.setAttribute("title", "انوبيس ويب");
  Safe.setAttribute("rel", 'noopener');
} else {
  window.location.href = "https://t.me/+xXqrrzgPGPJlMWI0";
}
console.group("%cSport GO Template", "font-weight:bold;color:#ef770f;font-family:Tahoma;font-size:18px;");
function _0x3def6c(_0x3a9e5c, _0x4bc0a3, _0x3bf5a3, _0x2db433) {
  return _0x20ec(_0x4bc0a3 + 0x375, _0x2db433);
}
console.log("» Designed by   : themex");
console.log("» Programmed by : themex");
console.log("» URL           : https://t.me/+xXqrrzgPGPJlMWI0");
function _0x1770() {
  var _0x3075d5 = ['querySelec', "em .TeamVS", 'uoHFj', 'createElem', "kground: #", "div#Menut ", 'czyXi', 'scrollTop', 'WEbju', 'MFVro', 'is-web.com', 'b24=', 'add', ';backgroun', 'px;border-', 'kjtRO', 'removeAttr', 'https://ww', '<!--clear-', "' data-src", 'insertAdja', '/png;base6', " by   : Mo", "teamlogo\" ", "' target=\"", 'click', 'document', "header .Me", 'mportant;t', 'contextmen', "title='", 'Nqvyd', 'bMkdi', 'slCCp', 'ts/default', 'GLsVw', 'body', 'Dark', 'EAdFz', 'pfUOO', ".loadmore ", 'left', 'AcnRs', 'XAQIF', 'seFGU', 'Soon', 'toDate', 'Q/YWx0PWpz', '.GP', 'display', 'uBnkp', 'gIkIa', 'data:image', "fe'><div c", "Image'><a ", "<h3 class=", 'IftGb', 'keydown', 'addEventLi', 'XcfRj', 'KNALJ', 'open', 'HqvBU', '?m=1', 'mHGEM', 'ault', 'EMpoP', 'rel', 'SnQMn', "» Designed", 'GgoAAAANSU', 'AUIsl', 'vFwsA', 'p);float:r', "_blanck\">ا", 'pOpkg', 'ustify-con', 'qJAOI', "نوبيس ويب<", 'GBoso', 'JDMJO', "f=\"/search", '[ASW]', 'vgzJj', 'data-id', '150imOZfy', 'fYudC', 'rl(//i.img', 'tTMZa', 'eiHTC', 'top', 'lDvIn', 'NeQce', "e><div cla", 'ly:Tahoma;', 'iZSUD', 'test', 'item', 'cwNjIyOTcx', " HH:mm:ssZ", '.UP', "' href=", 'JasNP', 'sbjef', 'YKWVH', 'line-block', 'XhTtk', 'iHcZS', 'term', 'kMABr', 'code', 'NpINO', 'bottom', 'fojSd', 'rYyRo', 'format', 'feed', "><img clas", " href=", 'bRnJH', 'VqXpx', 'gkRJt', 'href', '</ul></div', 'cFdZt', 'qhlUp', "='Posts'>", 'VaIlp', 'kTxUI', 'LOmrB', "ay: flex;j", 'link', 'dLbEC', 'gHkFO', 'length', 'scrollHeig', 'owUeM', 'FBuYp', 'YZgLM', ';}.NoneSaf', '724vcowKt', 's600-e90', "d-image: u", 'category', 'EvHOT', 'zrfct', 'URAHs', 'Hdphl', 'ById', 'und-image:', "eight: 700", 'MzkwMDg5Mz', 'BexPl', 'lPrsY', "div class=", 'GHTkg', 'XLixZ', 'rTyXU', 'RvSHA', ';font-fami', 'taXPq', '.loadmore-', 'iv></div><', 'KRvLt', 'GZDTo', "='item'><d", 'TfefG', 'NmDFC', "n: fixed;l", 'mEOOM', 'EIQug', '.ASW', 'LWQOw', 'RGFfg', 'ywKNb', 'suDqN', 'SRVSr', " class='la", 'BHRxm', 'torAll', 'FcvWE', 'ement', '1077633tnOCoX', 'kYOtQ', 'CJQmI', 'bVgBG', 'apaqf', 'eb.com', "rc='", 'fff;paddin', 'EDeKJ', "      : 2.", 'live', 'rKMvA', 'stener', 'userAgent', 'OIDUb', 'initial!im', " 0;bottom:", 'setAttribu', 'oTPJd', 'Live', 'SLOKy', 'theme', 'qEbCc', 'GhsOk', 'right', "v><div cla", 'eauZo', 'qGTHQ', 'TWZgb', 'style', '/-/', 'rcrvf', 'ackground-', 'Match', '!important', 'wEFEp', 'bnail', 'aHR0cHM6Ly', 'OGAUM', 'XJnKs', 'center;fon', 'RK5CYII=', "\"teamname\"", '</a></h3><', 'ulfhW', "g: 20px;te", 'a.Anubis-W', 'innerHTML', 'N0cy9kZWZh', 'bpEOj', "e>div {bac", "y: visible", 'yOCdY', 'tlalY', 'wpynI', 'location', 'ukgtE', "afe {displ", 'ylKXI', 'fpngl', 'eWDsv', 'rfrJZ', "md\"><div c", 'log', 'EOnZY', 'onload', 'round-posi', 'plDnb', 'KJQDm', 'rcVQA', 'xBEGZ', 'fFwgK', 'er;align-i', 'contentWin', 'PazEP', 'cmyqM', 'QRFpz', "ul li:firs", 'ULJoO', 'getAttribu', 'labels', 'SHawP', 'not', 'portant;wi', "e=\"backgro", 'TWsDk', '9125459sNxrXs', 'KeyI', "\" rel=\"tag", 'scroll', 'ASW', 'VHJUf', "olor: #222", "' class='l", ';visibilit', 'djWHV', 'IDlQb', 'cumZD', '/div>', 'Load_image', 'nDwJp', 'data-end', 'RHQqQ', "عنا لتفعيل", 'attachEven', 'o-repeat;b', "      : ht", 'anubis-web', 'remove', "ht: 0;top:", 'nuX', '302370kdGzEi', 'replaceSta', 'aLfTA', 'ctrlKey', "' alt='", 'Safe3', "tion: top;", "=\"coMatch\"", 'entry', 'QSLdl', '4461867bwnvZZ', 'Hnyiq', 'HBYTD', 'MmCqp', 'aIWgb', 'replace', 'bNaEs', 'aAfqB', 'xzbjv', 'xlwQT', 'PvTbX', "><img alt=", 'XtgTu', '</li><li>', "logo\" styl", 'oOEIP', 'xCvNM', 'exec', 'toggle', "lass=\"matc", 'JqxrU', 'preventDef', 'mqnCt', 'indexOf', 'iCbDv', 'hgTOF', 'history', 'VDokN', "ubis Web", "فعل الرجاء", 'forEach', 'data-src', 'XfNXV', 'vsLWK', 'HSVeP', 'DOMContent', 'NoMore', 'XsYEK', 't:bold;col', 'er;positio', 'Dejae', "» Version ", '18px;', 'WaVGO', 'getElement', '3121616hqPzlv', 'div#Menut', "lass=\"team", 'JbtQW', "afe'><p>ال", "ss='NoneSa", 'gAAAAASUVO', 'ACMAA2w/AM', 'clientWidt', 'getBoundin', '.lazy', 'utc', 'font-size:', 'XaAgL', 'jeZIA', 'DFQMV', '4366938tEACbl', 'json', 'stXAZ', 'Htcyd', 'PrkwK', "t-size: 16", 'body.Page', " a {font-w", 'documentEl', "><div clas", 'contains', 'dWx0Lzc5MD', "eft: 0;rig", 'gClientRec', 'YXetI', 'xmoXN', 'mCRXd', 'BKapd', 't-child', 'target', 'grhFr', 'zarpD', 'VESKP', 'classList', "انوبيس ويب", 'Easio', 'shiftKey', '/a></div><', "hamed Magd", "ransform: ", 'omNTU', 'tuFQX', 'groupEnd', "azy' src='", 'wlCQA', 'ibute', '?&alt=json', '.container', "tems: cent", "<div class", 'SZyYd', "'/></a><ul", 'auto;backg', 'centHTML', 'li>', 'ZnOiY', 'neSafe>div', 'xDglP', 'khIzP', 'MqAHa', "'title'><a", 'src', "قالب غير م", " Template", 'ANyMx', 'ihUAp', 'load', 'ssPjV', 'mvglJ', 'tor', 'title', 'yjslT', 'PtdU_d.web', '<style>.No', 'FvwGP', 'iVFyw', '.matchHour', '#nav-icon2', 'split', 'diff', 'moTFL', 'replaceAll', 'sAOcr'];
  _0x1770 = function () {
    return _0x3075d5;
  };
  return _0x1770();
}
function _0x43ad0b(_0x263f48, _0x2ec8ad, _0x46cb32, _0x6a72e8) {
  return _0x20ec(_0x263f48 + 0x2ef, _0x2ec8ad);
}
function _0x20ec(_0x4c8dc3, _0x5c4382) {
  var _0x20e4fe = _0x1770();
  _0x20ec = function (_0x2d6570, _0x4f4679) {
    _0x2d6570 = _0x2d6570 - 396;
    var _0x2b880f = _0x20e4fe[_0x2d6570];
    return _0x2b880f;
  };
  return _0x20ec(_0x4c8dc3, _0x5c4382);
}
console.log("» Version       : 2.8");
console.groupEnd();
/*]]>*/</b:tag></b:if>
<!-- noscript -->  
<b:include data='blog' name='google-analytics'/>
&lt;noscript id=&#39;blogger-components&#39;&gt;</body>&lt;/noscript&gt;&lt;/body&gt;
</html>