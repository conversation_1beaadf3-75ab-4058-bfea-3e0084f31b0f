#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ الإعدادات
Test saving settings functionality
"""

import json
import os
import sys
from datetime import datetime

def test_file_permissions():
    """اختبار صلاحيات الملفات"""
    print("🔍 اختبار صلاحيات الملفات...")
    
    test_file = "test_write_permission.tmp"
    
    try:
        # محاولة إنشاء ملف
        with open(test_file, "w", encoding="utf-8") as f:
            f.write("test")
        
        # محاولة قراءة الملف
        with open(test_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # حذف الملف
        os.remove(test_file)
        
        if content == "test":
            print("✅ صلاحيات الكتابة والقراءة متوفرة")
            return True
        else:
            print("❌ مشكلة في القراءة")
            return False
            
    except PermissionError:
        print("❌ لا توجد صلاحيات كتابة في هذا المجلد")
        print("💡 جرب تشغيل البرنامج كمدير")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
        return False

def test_json_operations():
    """اختبار عمليات JSON"""
    print("🔍 اختبار عمليات JSON...")
    
    test_data = {
        "daily_code": "TEST123",
        "channels": [
            {
                "name": "قناة اختبار",
                "m3u8_url": "https://example.com/test.m3u8",
                "activation_base_url": "https://example.com/activate.js?",
                "start_counter": 1000
            }
        ],
        "last_updated": datetime.now().isoformat(),
        "version": "1.0",
        "admin_settings": {
            "data_url": "https://example.com/data.json",
            "admin_code": "ADMIN2024"
        }
    }
    
    test_file = "test_data.json"
    
    try:
        # كتابة البيانات
        with open(test_file, "w", encoding="utf-8") as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print("✅ تم كتابة ملف JSON")
        
        # قراءة البيانات
        with open(test_file, "r", encoding="utf-8") as f:
            loaded_data = json.load(f)
        
        print("✅ تم قراءة ملف JSON")
        
        # التحقق من البيانات
        if loaded_data == test_data:
            print("✅ البيانات متطابقة")
            success = True
        else:
            print("❌ البيانات غير متطابقة")
            success = False
        
        # حذف الملف
        os.remove(test_file)
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في عمليات JSON: {e}")
        return False

def test_data_file():
    """اختبار ملف البيانات الحالي"""
    print("🔍 اختبار ملف البيانات الحالي...")
    
    if not os.path.exists("data.json"):
        print("⚠️ ملف data.json غير موجود")
        return False
    
    try:
        # قراءة الملف
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        print("✅ تم قراءة ملف البيانات")
        
        # التحقق من البنية
        required_fields = ["daily_code", "channels"]
        for field in required_fields:
            if field not in data:
                print(f"❌ الحقل المطلوب غير موجود: {field}")
                return False
        
        print("✅ بنية البيانات صحيحة")
        
        # عرض معلومات الملف
        file_size = os.path.getsize("data.json")
        file_time = datetime.fromtimestamp(os.path.getmtime("data.json"))
        
        print(f"📁 حجم الملف: {file_size} بايت")
        print(f"🕒 آخر تعديل: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📺 عدد القنوات: {len(data.get('channels', []))}")
        print(f"🔐 كود التفعيل: {data.get('daily_code', 'غير محدد')}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ خطأ في تنسيق JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def create_backup():
    """إنشاء نسخة احتياطية"""
    print("💾 إنشاء نسخة احتياطية...")
    
    if not os.path.exists("data.json"):
        print("⚠️ لا يوجد ملف للنسخ الاحتياطي")
        return False
    
    try:
        backup_name = f"data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open("data.json", "r", encoding="utf-8") as source:
            with open(backup_name, "w", encoding="utf-8") as backup:
                backup.write(source.read())
        
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return False

def test_save_functionality():
    """اختبار وظيفة الحفظ الكاملة"""
    print("🧪 اختبار وظيفة الحفظ الكاملة...")
    
    # إنشاء بيانات اختبار
    test_data = {
        "daily_code": "SAVE_TEST",
        "channels": [
            {
                "name": "قناة اختبار الحفظ",
                "m3u8_url": "https://test.com/save.m3u8",
                "activation_base_url": "https://test.com/save.js?",
                "start_counter": 9999
            }
        ],
        "last_updated": datetime.now().isoformat(),
        "version": "1.0",
        "admin_settings": {
            "data_url": "https://test.com/save_data.json",
            "admin_code": "SAVE_ADMIN"
        }
    }
    
    # حفظ نسخة احتياطية من الملف الأصلي
    original_backup = None
    if os.path.exists("data.json"):
        with open("data.json", "r", encoding="utf-8") as f:
            original_backup = f.read()
    
    try:
        # كتابة بيانات الاختبار
        with open("data.json", "w", encoding="utf-8") as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print("✅ تم حفظ بيانات الاختبار")
        
        # قراءة البيانات للتحقق
        with open("data.json", "r", encoding="utf-8") as f:
            saved_data = json.load(f)
        
        # التحقق من التطابق
        if saved_data["daily_code"] == "SAVE_TEST":
            print("✅ تم حفظ البيانات بنجاح")
            success = True
        else:
            print("❌ البيانات لم تُحفظ بشكل صحيح")
            success = False
        
        # استعادة الملف الأصلي
        if original_backup:
            with open("data.json", "w", encoding="utf-8") as f:
                f.write(original_backup)
            print("✅ تم استعادة الملف الأصلي")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحفظ: {e}")
        
        # محاولة استعادة الملف الأصلي
        if original_backup:
            try:
                with open("data.json", "w", encoding="utf-8") as f:
                    f.write(original_backup)
                print("✅ تم استعادة الملف الأصلي")
            except:
                print("❌ فشل في استعادة الملف الأصلي")
        
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار حفظ الإعدادات - Live TV Viewer")
    print("=" * 60)
    print()
    
    tests = [
        ("اختبار صلاحيات الملفات", test_file_permissions),
        ("اختبار عمليات JSON", test_json_operations),
        ("اختبار ملف البيانات الحالي", test_data_file),
        ("اختبار وظيفة الحفظ الكاملة", test_save_functionality)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
            failed += 1
        print()
    
    # النتائج
    print("=" * 60)
    print(f"📊 نتائج الاختبارات:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {passed/(passed+failed)*100:.1f}%")
    print()
    
    if failed == 0:
        print("🎉 جميع اختبارات الحفظ نجحت!")
        print("💡 يجب أن تعمل وظيفة حفظ الإعدادات بشكل صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("💡 راجع ملف TROUBLESHOOTING.md للحلول")
    
    # إنشاء نسخة احتياطية
    print()
    create_backup()
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'✅' if success else '❌'} اضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
