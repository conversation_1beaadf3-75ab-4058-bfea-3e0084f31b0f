# 📺 مشاهد القنوات المباشرة - Live TV Viewer

برنامج سطح مكتب لمشاهدة القنوات المباشرة مع نظام تفعيل يومي وإدارة متقدمة.

## ✨ الميزات الرئيسية

### للمستخدم العادي:
- 🔐 **نظام تفعيل يومي**: كود تفعيل يتغير كل 24 ساعة
- 📺 **قائمة القنوات**: عرض جميع القنوات المتاحة
- ▶️ **تشغيل مباشر**: تشغيل القنوات باستخدام VLC أو المشغل الافتراضي
- 🔄 **تفعيل تلقائي**: نظام تفعيل في الخلفية للحفاظ على البث
- 📱 **واجهة سهلة**: تصميم بسيط وسهل الاستخدام

### للإدمن:
- ⚙️ **لوحة تحكم شاملة**: إدارة كاملة للنظام
- 📺 **إدارة القنوات**: إضافة، تعديل، وحذف القنوات
- 🔐 **إدارة الأكواد**: إنشاء وتحديث أكواد التفعيل
- ☁️ **رفع البيانات**: تحديث ملف البيانات على الإنترنت
- 👁️ **معاينة البيانات**: عرض البيانات بصيغة JSON

## 🚀 التثبيت والتشغيل

### المتطلبات:
- Windows 7/8/10/11
- Python 3.6 أو أحدث
- اتصال بالإنترنت

### خطوات التثبيت:

1. **تحميل الملفات:**
   ```
   live_tv_app.py
   data.json
   run_app.bat
   requirements.txt
   ```

2. **تثبيت Python:**
   - تحميل من: https://python.org
   - تأكد من تفعيل "Add to PATH"

3. **تثبيت المكتبات:**
   ```bash
   pip install -r requirements.txt
   ```

4. **تشغيل البرنامج:**
   - انقر مرتين على `run_app.bat`
   - أو استخدم: `python live_tv_app.py`

## 🔧 الاستخدام

### للمستخدم العادي:

1. **التفعيل:**
   - أدخل كود التفعيل اليومي
   - اضغط "تفعيل"

2. **مشاهدة القنوات:**
   - اختر قناة من القائمة
   - اضغط "ابدأ المشاهدة"
   - سيتم تشغيل القناة في VLC

### للإدمن:

1. **الدخول لوحة الإدمن:**
   - اضغط "الإعدادات"
   - أدخل كود الإدمن: `ADMIN2024`

2. **إدارة القنوات:**
   - تبويب "إدارة القنوات"
   - إضافة/تعديل/حذف القنوات

3. **إدارة الأكواد:**
   - تبويب "إدارة الأكواد"
   - توليد أكواد جديدة
   - تحديث الكود اليومي

4. **رفع البيانات:**
   - تبويب "رفع البيانات"
   - معاينة البيانات
   - حفظ محلياً أو رفع للإنترنت

## 📋 بنية البيانات

### ملف data.json:
```json
{
  "daily_code": "TEST123",
  "channels": [
    {
      "name": "اسم القناة",
      "m3u8_url": "رابط البث",
      "activation_base_url": "رابط التفعيل الأساسي",
      "start_counter": 1000
    }
  ],
  "last_updated": "2024-01-15T10:30:00",
  "version": "1.0"
}
```

## 🔐 الأمان

- **كود الإدمن**: `ADMIN2024` (يمكن تغييره في الكود)
- **كود التفعيل**: يتغير يومياً أو حسب الحاجة
- **التفعيل التلقائي**: يعمل في الخلفية بشكل آمن

## 🛠️ التخصيص

### تغيير كود الإدمن:
```python
self.admin_code = "كود_جديد"
```

### تغيير رابط البيانات:
```python
self.data_url = "رابط_ملف_البيانات"
```

### تغيير فترة التفعيل:
```python
time.sleep(2)  # ثانيتين (في دالة activation_worker)
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **"Python غير مثبت":**
   - تثبيت Python من الموقع الرسمي
   - إعادة تشغيل الكمبيوتر

2. **"فشل في تحميل البيانات":**
   - التحقق من الاتصال بالإنترنت
   - التحقق من صحة رابط البيانات

3. **"VLC غير موجود":**
   - تثبيت VLC Media Player
   - أو استخدام المشغل الافتراضي

4. **"خطأ في التفعيل":**
   - التحقق من صحة كود التفعيل
   - التحقق من التاريخ والوقت

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- تواصل مع فريق التطوير
- تحقق من ملف الأخطاء (إن وجد)

## 📄 الترخيص

هذا البرنامج مخصص للاستخدام الشخصي والتعليمي فقط.

---

**تم التطوير بواسطة:** فريق التطوير  
**الإصدار:** 1.0  
**تاريخ التحديث:** يناير 2024
