import tkinter as tk
from tkinter import messagebox
import threading
import time
import requests
import re

class KeepAliveApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تطبيق إبقاء الروابط مفعلة")

        # واجهة المستخدم
        tk.Label(root, text="رابط التفعيل (.js):").pack()
        self.js_entry = tk.Entry(root, width=60)
        self.js_entry.pack()

        tk.Label(root, text="الفاصل الزمني (بالثواني):").pack()
        self.interval_entry = tk.Entry(root, width=10)
        self.interval_entry.insert(0, "60")
        self.interval_entry.pack()

        self.start_button = tk.Button(root, text="ابدأ", command=self.start_sending)
        self.start_button.pack(pady=10)

        self.running = False

    def send_js_link(self):
        current_url = self.js_url
        match = re.search(r"(.*?)(\d+)$", current_url)
        if not match:
            print("تعذر التعرف على الرقم في نهاية الرابط.")
            return

        base_url = match.group(1)
        current_number = int(match.group(2))

        while self.running:
            full_url = f"{base_url}{current_number}"
            try:
                print(f"إرسال: {full_url}")
                response = requests.get(full_url, timeout=10)
                print("تم الإرسال، الحالة:", response.status_code)
            except Exception as e:
                print("خطأ:", e)

            current_number += 1  # زيادة الرقم في كل مرة
            time.sleep(self.interval)

    def start_sending(self):
        self.js_url = self.js_entry.get().strip()
        try:
            self.interval = int(self.interval_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "أدخل رقمًا صحيحًا للفاصل الزمني.")
            return

        if not self.js_url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط التفعيل.")
            return

        self.running = True
        threading.Thread(target=self.send_js_link, daemon=True).start()
        self.start_button.config(state=tk.DISABLED)
        messagebox.showinfo("بدء", "بدأ التطبيق بإرسال رابط التفعيل مع تحديث الرقم تلقائيًا.")

# تشغيل التطبيق
if __name__ == "__main__":
    root = tk.Tk()
    app = KeepAliveApp(root)
    root.mainloop()
