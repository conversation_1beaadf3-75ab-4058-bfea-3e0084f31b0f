<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المشغلات</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .post-body {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .video-serv {
            margin-top: 15px;
            overflow: hidden;
            width: 100%;
            margin-bottom: 5px;
            clear: both;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 5px;
        }
        .video-serv a {
            background: #931800;
            border-radius: 4px;
            text-shadow: 0 1px 1px #000;
            color: #fff !important;
            font-weight: 700;
            padding: 5px 15px;
            font-size: 16px;
            flex: 1;
            text-align: center;
            margin: 3px;
            transition: all 0.3s ease;
            min-width: 120px;
            text-decoration: none;
        }
        .video-serv a:hover {
            background: #b01e00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        #player-container {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
            position: relative;
        }
        #player-container video,
        #player-container #dplayer,
        #player-container #player {
            width: 100% !important;
            height: 100% !important;
            border-radius: 8px;
        }
        @media screen and (max-width: 768px) {
            .video-serv {
                flex-direction: column;
                gap: 8px;
            }
            .video-serv a {
                margin: 2px 0;
                min-width: auto;
                width: 100%;
            }
            #player-container {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام المشغلات المتعددة</h1>
        
        <div class="post-body">
            <h2>مثال على رابط m3u8:</h2>
            <p>https://re3yaa.com/p/test.html?blender.m3u8</p>
            
            <h2>مثال آخر:</h2>
            <p>https://example.com/stream.m3u8</p>
        </div>
        
        <div id="instructions">
            <h3>التعليمات:</h3>
            <ol>
                <li>سيتم تحويل الروابط أعلاه تلقائياً إلى مشغلات متعددة</li>
                <li>اختر المشغل المناسب لك</li>
                <li>إذا لم يعمل مشغل، جرب مشغل آخر</li>
            </ol>
        </div>
    </div>

    <script>
        // كود تحويل روابط mu38 إلى مشغلات متعددة
        $(document).ready(function() {
            // البحث عن الروابط التي تحتوي على mu38 أو m3u8
            $('.post-body').each(function() {
                var postContent = $(this).html();
                
                // البحث عن الروابط التي تحتوي على mu38 أو m3u8 أو blender
                var streamRegex = /(https?:\/\/[^\s<>"]+(?:\.m3u8|blender\.m3u8|mu38)[^\s<>"]*)/gi;
                var matches = postContent.match(streamRegex);
                
                if (matches && matches.length > 0) {
                    var streamUrl = matches[0];
                    
                    // إنشاء المشغلات المتعددة
                    var playersHtml = '<div class="video-serv">';
                    playersHtml += '<a href="?m=1&url=' + encodeURIComponent(streamUrl) + '">مشغل 1 - DPlayer</a>';
                    playersHtml += '<a href="?m=2&url=' + encodeURIComponent(streamUrl) + '">مشغل 2 - Plyr</a>';
                    playersHtml += '<a href="?m=3&url=' + encodeURIComponent(streamUrl) + '">مشغل 3 - Video.js</a>';
                    playersHtml += '<a href="?m=4&url=' + encodeURIComponent(streamUrl) + '">مشغل 4 - Clappr</a>';
                    playersHtml += '<a href="?m=5&url=' + encodeURIComponent(streamUrl) + '">مشغل 5 - JWPlayer</a>';
                    playersHtml += '<a href="?m=6&url=' + encodeURIComponent(streamUrl) + '">مشغل 6 - HTML5</a>';
                    playersHtml += '</div>';
                    
                    // إضافة رسالة توضيحية
                    playersHtml += '<div style="text-align:center;margin:10px 0;padding:10px;background:#e8f5e8;border-radius:5px;color:#2d5a2d;border:1px solid #4caf50;">';
                    playersHtml += '<strong>✅ تم العثور على رابط البث:</strong> اختر المشغل المناسب أدناه';
                    playersHtml += '</div>';
                    
                    // استبدال الرابط الأصلي بالمشغلات
                    var newContent = postContent.replace(streamUrl, playersHtml);
                    $(this).html(newContent);
                }
                
                // البحث عن روابط re3yaa.com مع blender
                var re3yaaRegex = /(https?:\/\/re3yaa\.com\/p\/[^?]*\?blender\.m3u8)/gi;
                var re3yaaMatches = postContent.match(re3yaaRegex);
                
                if (re3yaaMatches && re3yaaMatches.length > 0) {
                    var originalUrl = re3yaaMatches[0];
                    // استخراج الرابط الأساسي
                    var baseUrl = originalUrl.replace('?blender.m3u8', '');
                    
                    // إنشاء المشغلات المتعددة
                    var playersHtml = '<div class="video-serv">';
                    playersHtml += '<a href="' + baseUrl + '?m=1">مشغل 1 - DPlayer</a>';
                    playersHtml += '<a href="' + baseUrl + '?m=2">مشغل 2 - Plyr</a>';
                    playersHtml += '<a href="' + baseUrl + '?m=3">مشغل 3 - Video.js</a>';
                    playersHtml += '<a href="' + baseUrl + '?m=4">مشغل 4 - Clappr</a>';
                    playersHtml += '<a href="' + baseUrl + '?m=5">مشغل 5 - JWPlayer</a>';
                    playersHtml += '<a href="' + baseUrl + '?m=6">مشغل 6 - HTML5</a>';
                    playersHtml += '</div>';
                    
                    // استبدال الرابط الأصلي بالمشغلات
                    var newContent = postContent.replace(originalUrl, playersHtml);
                    $(this).html(newContent);
                }
            });
            
            // التعامل مع معاملات URL للمشغلات
            var urlParams = new URLSearchParams(window.location.search);
            var playerType = urlParams.get('m');
            var streamUrl = urlParams.get('url');
            
            if (playerType && streamUrl) {
                // إخفاء التعليمات
                $('#instructions').hide();
                
                // إضافة زر العودة
                var backButton = '<div style="text-align:center;margin:10px 0;">';
                backButton += '<a href="' + window.location.pathname + '" style="background:#931800;color:#fff;padding:8px 20px;border-radius:5px;text-decoration:none;font-weight:bold;">← العودة لاختيار المشغل</a>';
                backButton += '</div>';
                
                $('.post-body').html(backButton + '<div id="player-container"></div>');
                
                // إضافة عنوان المشغل
                var playerNames = {
                    '1': 'DPlayer',
                    '2': 'Plyr', 
                    '3': 'Video.js',
                    '4': 'Clappr',
                    '5': 'JWPlayer',
                    '6': 'HTML5'
                };
                
                var playerTitle = '<div style="text-align:center;margin:10px 0;padding:10px;background:#e3f2fd;border-radius:5px;border:1px solid #2196f3;">';
                playerTitle += '<h3 style="margin:0;color:#1976d2;">🎬 المشغل الحالي: ' + playerNames[playerType] + '</h3>';
                playerTitle += '<p style="margin:5px 0 0 0;color:#666;">إذا لم يعمل المشغل، ارجع واختر مشغل آخر</p>';
                playerTitle += '</div>';
                
                $('#player-container').before(playerTitle);
                
                // تحميل المشغل المناسب
                switch(playerType) {
                    case '1':
                        loadPlayer1(streamUrl);
                        break;
                    case '2':
                        loadPlayer2(streamUrl);
                        break;
                    case '3':
                        loadPlayer3(streamUrl);
                        break;
                    case '4':
                        loadPlayer4(streamUrl);
                        break;
                    case '5':
                        loadPlayer5(streamUrl);
                        break;
                    case '6':
                        loadPlayer6(streamUrl);
                        break;
                }
            }
        });

        // دالة تحميل مشغل HTML5 بسيط للاختبار
        function loadPlayer6(url) {
            $('#player-container').html('<video id="player" controls autoplay width="100%" height="400" style="background:#000;"><source src="' + url + '" type="application/x-mpegURL">متصفحك لا يدعم تشغيل الفيديو</video>');
        }

        // يمكن إضافة باقي دوال المشغلات هنا حسب الحاجة
        function loadPlayer1(url) {
            $('#player-container').html('<div style="color:#fff;text-align:center;padding:50px;">مشغل DPlayer - سيتم تحميله في النسخة الكاملة</div>');
        }

        function loadPlayer2(url) {
            $('#player-container').html('<div style="color:#fff;text-align:center;padding:50px;">مشغل Plyr - سيتم تحميله في النسخة الكاملة</div>');
        }

        function loadPlayer3(url) {
            $('#player-container').html('<div style="color:#fff;text-align:center;padding:50px;">مشغل Video.js - سيتم تحميله في النسخة الكاملة</div>');
        }

        function loadPlayer4(url) {
            $('#player-container').html('<div style="color:#fff;text-align:center;padding:50px;">مشغل Clappr - سيتم تحميله في النسخة الكاملة</div>');
        }

        function loadPlayer5(url) {
            $('#player-container').html('<div style="color:#fff;text-align:center;padding:50px;">مشغل JWPlayer - سيتم تحميله في النسخة الكاملة</div>');
        }
    </script>
</body>
</html>
