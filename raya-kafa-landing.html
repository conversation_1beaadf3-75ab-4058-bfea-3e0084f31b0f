<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>راية كافية - تطبيق مشاهدة المباريات المباشرة</title>
    <meta name="description" content="حمل تطبيق راية كافية لمشاهدة المباريات المباشرة مجاناً. أفضل تطبيق لمتابعة كرة القدم والقنوات الرياضية بجودة عالية">
    <meta name="keywords" content="راية كافية, مشاهدة المباريات, بث مباشر, كرة القدم, قنوات رياضية">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="راية كافية - تطبيق مشاهدة المباريات المباشرة">
    <meta property="og:description" content="حمل تطبيق راية كافية لمشاهدة المباريات المباشرة مجاناً">
    <meta property="og:type" content="website">
    <meta property="og:image" content="https://via.placeholder.com/1200x630/1a73e8/ffffff?text=راية+كافية">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://via.placeholder.com/32x32/1a73e8/ffffff?text=R">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #ea4335;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --gradient: linear-gradient(135deg, #1a73e8 0%, #34a853 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            overflow-x: hidden;
        }

        .hero-section {
            background: var(--gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .download-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .download-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            color: white;
        }

        .features-section {
            padding: 5rem 0;
            background: var(--light-color);
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: var(--dark-color);
        }

        .screenshots-section {
            padding: 5rem 0;
            background: white;
        }

        .screenshot-card {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .screenshot-card:hover {
            transform: scale(1.05);
        }

        .screenshot-card img {
            width: 100%;
            height: auto;
            display: block;
        }

        .stats-section {
            background: var(--gradient);
            padding: 3rem 0;
            color: white;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .footer {
            background: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            color: white;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .download-btn {
                width: 100%;
                max-width: 300px;
            }
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="floating-elements">
            <i class="fas fa-futbol floating-element" style="font-size: 4rem;"></i>
            <i class="fas fa-tv floating-element" style="font-size: 3rem;"></i>
            <i class="fas fa-mobile-alt floating-element" style="font-size: 3.5rem;"></i>
        </div>
        
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="hero-content" data-aos="fade-up">
                        <h1 class="hero-title">راية كافية</h1>
                        <p class="hero-subtitle">
                            أفضل تطبيق لمشاهدة المباريات المباشرة والقنوات الرياضية مجاناً<br>
                            استمتع بمتابعة فريقك المفضل بجودة عالية وبدون انقطاع
                        </p>
                        
                        <div class="download-buttons">
                            <a href="#" class="download-btn" data-aos="fade-up" data-aos-delay="100">
                                <i class="fab fa-android"></i>
                                تحميل للأندرويد
                            </a>
                            <a href="#" class="download-btn" data-aos="fade-up" data-aos-delay="200">
                                <i class="fas fa-desktop"></i>
                                تحميل للكمبيوتر
                            </a>
                            <a href="#" class="download-btn" data-aos="fade-up" data-aos-delay="300">
                                <i class="fas fa-tv"></i>
                                للتلفاز الذكي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6 mb-3">
                    <div class="stat-item" data-aos="fade-up">
                        <span class="stat-number">1M+</span>
                        <span class="stat-label">مستخدم نشط</span>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-3">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">قناة متاحة</span>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-3">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">بث مستمر</span>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-3">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
                        <span class="stat-number">4.8★</span>
                        <span class="stat-label">تقييم المستخدمين</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">مميزات التطبيق</h2>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-icon">
                            <i class="fas fa-futbol"></i>
                        </div>
                        <h4>مباريات مباشرة</h4>
                        <p>شاهد جميع المباريات المهمة مباشرة بجودة عالية وبدون انقطاع. تغطية شاملة لجميع الدوريات العالمية والمحلية.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h4>قنوات متنوعة</h4>
                        <p>أكثر من 500 قناة رياضية وترفيهية وإخبارية. قنوات عربية وعالمية بجودات مختلفة تناسب سرعة الإنترنت لديك.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>متوافق مع جميع الأجهزة</h4>
                        <p>يعمل على الهواتف الذكية والأجهزة اللوحية والكمبيوتر والتلفاز الذكي. تجربة مشاهدة مثالية على أي جهاز.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-icon">
                            <i class="fas fa-hd-video"></i>
                        </div>
                        <h4>جودة عالية</h4>
                        <p>بث بجودة HD و Full HD. إمكانية تغيير الجودة حسب سرعة الإنترنت لضمان مشاهدة سلسة بدون تقطيع.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
                        <div class="feature-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h4>تنبيهات المباريات</h4>
                        <p>احصل على تنبيهات قبل بداية المباريات المهمة. لن تفوت أي مباراة لفريقك المفضل مع نظام التنبيهات الذكي.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
                        <div class="feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h4>تعليق متعدد اللغات</h4>
                        <p>تعليق عربي وإنجليزي وفرنسي وتركي. اختر اللغة التي تفضلها لمتابعة المباريات والبرامج الرياضية.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Screenshots Section -->
    <section class="screenshots-section">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">لقطات من التطبيق</h2>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="screenshot-card" data-aos="fade-up" data-aos-delay="100">
                        <img src="https://via.placeholder.com/300x600/1a73e8/ffffff?text=الشاشة+الرئيسية" alt="الشاشة الرئيسية">
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="screenshot-card" data-aos="fade-up" data-aos-delay="200">
                        <img src="https://via.placeholder.com/300x600/34a853/ffffff?text=المباريات+المباشرة" alt="المباريات المباشرة">
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="screenshot-card" data-aos="fade-up" data-aos-delay="300">
                        <img src="https://via.placeholder.com/300x600/ea4335/ffffff?text=القنوات+التلفزيونية" alt="القنوات التلفزيونية">
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="screenshot-card" data-aos="fade-up" data-aos-delay="400">
                        <img src="https://via.placeholder.com/300x600/fbbc04/ffffff?text=الإعدادات" alt="الإعدادات">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="features-section">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">الأسئلة الشائعة</h2>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item mb-3" data-aos="fade-up" data-aos-delay="100">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    هل التطبيق مجاني؟
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، تطبيق راية كافية مجاني تماماً. يمكنك تحميله واستخدامه بدون أي رسوم أو اشتراكات.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3" data-aos="fade-up" data-aos-delay="200">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    ما هي متطلبات التشغيل؟
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يتطلب التطبيق نظام أندرويد 4.2 أو أحدث، واتصال إنترنت مستقر. للحصول على أفضل تجربة، ننصح بسرعة إنترنت 2 ميجابت على الأقل.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3" data-aos="fade-up" data-aos-delay="300">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    هل يمكن استخدام التطبيق على التلفاز؟
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، يدعم التطبيق Chromecast ويمكن تشغيله على التلفاز الذكي. كما يتوفر إصدار خاص للتلفاز الذكي.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item mb-3" data-aos="fade-up" data-aos-delay="400">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    كيف أحل مشكلة التقطيع في البث؟
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    تأكد من قوة الإنترنت، قم بتقليل جودة البث، أعد تشغيل التطبيق، أو امسح ذاكرة التخزين المؤقت للتطبيق.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5>راية كافية</h5>
                    <p>أفضل تطبيق لمشاهدة المباريات المباشرة والقنوات الرياضية. استمتع بمتابعة فريقك المفضل بجودة عالية ومجاناً.</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>روابط سريعة</h6>
                    <ul class="footer-links">
                        <li><a href="#hero">الرئيسية</a></li>
                        <li><a href="#features">المميزات</a></li>
                        <li><a href="#screenshots">لقطات الشاشة</a></li>
                        <li><a href="#faq">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>التحميل</h6>
                    <ul class="footer-links">
                        <li><a href="#">للأندرويد</a></li>
                        <li><a href="#">للكمبيوتر</a></li>
                        <li><a href="#">للآيفون</a></li>
                        <li><a href="#">للتلفاز الذكي</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>الدعم</h6>
                    <ul class="footer-links">
                        <li><a href="#">مركز المساعدة</a></li>
                        <li><a href="#">اتصل بنا</a></li>
                        <li><a href="#">الإبلاغ عن مشكلة</a></li>
                        <li><a href="#">طلب قناة</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>قانوني</h6>
                    <ul class="footer-links">
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">الشروط والأحكام</a></li>
                        <li><a href="#">إخلاء المسؤولية</a></li>
                        <li><a href="#">حقوق النشر</a></li>
                    </ul>
                </div>
            </div>
            <hr style="border-color: rgba(255,255,255,0.1); margin: 2rem 0 1rem;">
            <div class="row">
                <div class="col-12 text-center">
                    <p style="margin: 0; opacity: 0.7;">
                        © 2024 راية كافية. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to navbar (if you add one later)
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Counter animation for stats
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = counter.innerText;
                const numericTarget = parseInt(target.replace(/[^\d]/g, ''));
                const suffix = target.replace(/[\d]/g, '');

                let current = 0;
                const increment = numericTarget / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numericTarget) {
                        current = numericTarget;
                        clearInterval(timer);
                    }
                    counter.innerText = Math.floor(current) + suffix;
                }, 20);
            });
        }

        // Trigger counter animation when stats section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });
    </script>
</body>
</html>
