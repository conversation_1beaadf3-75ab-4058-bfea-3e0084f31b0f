# 🚀 دليل البدء السريع - Live TV Viewer

## 📥 التحميل والتثبيت

### الطريقة الأولى: تشغيل مباشر (Python)
1. تحميل جميع الملفات
2. تثبيت Python من https://python.org
3. تشغيل: `python live_tv_app.py`

### الطريقة الثانية: ملف EXE محمول
1. تشغيل: `python build.py`
2. استخدام الملف من مجلد `LiveTVViewer_Portable`

## 🔐 أكواد الوصول

### للمستخدم العادي:
- **كود التفعيل التجريبي:** `TEST123`

### للإدمن:
- **كود الإدمن:** `ADMIN2024`

## 📺 الاستخدام السريع

### 1. تفعيل البرنامج:
```
1. تشغيل البرنامج
2. إد<PERSON><PERSON><PERSON> كود التفعيل: TEST123
3. الضغط على "تفعيل"
```

### 2. مشاهدة القنوات:
```
1. اختيار قناة من القائمة
2. الضغط على "ابدأ المشاهدة"
3. سيتم تشغيل القناة في VLC
```

### 3. الوصول للإدمن:
```
1. الضغط على "الإعدادات"
2. إدخال كود الإدمن: ADMIN2024
3. إدارة القنوات والأكواد
```

## ⚙️ إعدادات سريعة

### إضافة قناة جديدة (للإدمن):
```
1. لوحة الإدمن → تبويب "إدارة القنوات"
2. الضغط على "إضافة قناة"
3. ملء البيانات:
   - اسم القناة
   - رابط M3U8
   - رابط التفعيل
   - رقم البداية
4. حفظ
```

### تغيير كود التفعيل:
```
1. لوحة الإدمن → تبويب "إدارة الأكواد"
2. إدخال كود جديد أو الضغط على "توليد عشوائي"
3. الضغط على "تحديث الكود"
```

## 🛠️ حل المشاكل السريع

### المشكلة: "Python غير مثبت"
**الحل:** تثبيت Python من https://python.org

### المشكلة: "VLC غير موجود"
**الحل:** تثبيت VLC Media Player أو استخدام المشغل الافتراضي

### المشكلة: "كود التفعيل خاطئ"
**الحل:** استخدام `TEST123` أو التواصل مع الإدمن

### المشكلة: "فشل في تحميل البيانات"
**الحل:** التحقق من الاتصال بالإنترنت أو استخدام البيانات المحلية

## 📋 قائمة المراجعة السريعة

### قبل التشغيل:
- [ ] Python مثبت (للتشغيل المباشر)
- [ ] VLC مثبت (اختياري)
- [ ] اتصال بالإنترنت متوفر
- [ ] جميع الملفات موجودة

### للاختبار:
- [ ] تشغيل البرنامج
- [ ] تفعيل بكود TEST123
- [ ] اختبار قناة واحدة
- [ ] الدخول للوحة الإدمن

## 🔧 تخصيص سريع

### تغيير الأكواد في الملف:
```python
# في live_tv_app.py
self.admin_code = "كود_إدمن_جديد"
self.daily_code = "كود_تفعيل_جديد"
```

### إضافة قنوات في data.json:
```json
{
  "name": "اسم القناة",
  "m3u8_url": "رابط البث",
  "activation_base_url": "رابط التفعيل",
  "start_counter": 1000
}
```

## 📞 الدعم السريع

### للمطورين:
- تحقق من ملف `live_tv_app.py`
- راجع ملف `data.json`
- استخدم وضع debug في Python

### للمستخدمين:
- استخدم الأكواد التجريبية
- تواصل مع الإدمن للحصول على أكواد جديدة
- تأكد من تثبيت المتطلبات

---

**💡 نصيحة:** احتفظ بنسخة احتياطية من ملف `data.json` قبل إجراء أي تعديلات!
