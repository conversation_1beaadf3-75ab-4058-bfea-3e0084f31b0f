@echo off
chcp 65001 >nul
title Live TV Viewer - تشغيل مبسط
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        🔴 مشاهد القنوات المباشرة - Live TV Viewer        ██
echo ██                     تشغيل مبسط                           ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM البحث عن أفضل طريقة للتشغيل
echo 🔍 البحث عن أفضل طريقة للتشغيل...
echo.

REM الأولوية الأولى: الإصدار المحمول
if exist "LiveTVViewer_Portable\LiveTVViewer.exe" (
    echo ✅ تم العثور على الإصدار المحمول
    echo 🚀 جاري التشغيل...
    echo.
    cd LiveTVViewer_Portable
    start "" "LiveTVViewer.exe"
    cd ..
    echo ✅ تم تشغيل البرنامج بنجاح
    goto end
)

REM الأولوية الثانية: ملف EXE في مجلد dist
if exist "dist\LiveTVViewer.exe" (
    echo ✅ تم العثور على ملف EXE
    echo 🚀 جاري التشغيل...
    echo.
    start "" "dist\LiveTVViewer.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
    goto end
)

REM الأولوية الثالثة: تشغيل بـ Python
echo 🐍 تشغيل بـ Python...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 💡 حلول مقترحة:
    echo    1. تثبيت Python من https://python.org
    echo    2. أو استخدام الإصدار المحمول (EXE)
    echo.
    goto error_end
)

echo ✅ Python متوفر
echo 🚀 جاري التشغيل...
echo.

REM تشغيل البرنامج
if exist "run_standalone.py" (
    python run_standalone.py
) else if exist "live_tv_app.py" (
    python live_tv_app.py
) else (
    echo ❌ ملفات البرنامج غير موجودة
    goto error_end
)

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    goto error_end
) else (
    echo ✅ تم تشغيل البرنامج بنجاح
    goto end
)

:error_end
echo.
echo 🔧 للحصول على المساعدة:
echo    - تشغيل START_HERE.bat للخيارات المتقدمة
echo    - مراجعة ملف README.md
echo.
pause
exit /b 1

:end
echo.
echo 💡 معلومات مفيدة:
echo    - كود التفعيل: TEST123
echo    - كود الإدمن: ADMIN2024
echo.
echo 👋 يمكنك إغلاق هذه النافذة الآن
echo.
timeout /t 3 >nul
exit /b 0
