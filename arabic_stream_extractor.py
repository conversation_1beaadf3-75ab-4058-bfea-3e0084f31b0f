#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة متخصصة لاستخراج روابط البث من المواقع العربية
Arabic Streaming Sites Specialized Extractor
"""

import requests
import re
import json
import base64
from urllib.parse import urljoin, urlparse, unquote
from bs4 import BeautifulSoup
import time
import os

class ArabicStreamExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
        })
        
        # قائمة بالمواقع العربية المعروفة
        self.arabic_sites = [
            '3rbcafee.com', 'yallakora.com', 'koralive.com',
            'yalla-shoot.com', 'kooralive.net', 'koora-online.tv'
        ]
        
    def analyze_3rbcafee_player(self, html_content):
        """تحليل مشغل موقع عرب كافيه"""
        streams = []
        
        print("🔍 تحليل مشغل عرب كافيه...")
        
        # البحث عن روابط في JavaScript
        js_patterns = [
            r'src\s*[:=]\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'source\s*[:=]\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'file\s*[:=]\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'url\s*[:=]\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                
                if self.is_valid_stream_url(match):
                    streams.append({
                        'url': match,
                        'type': 'HLS (m3u8)',
                        'source': '3rbcafee',
                        'quality': self.detect_quality(match)
                    })
        
        # البحث عن روابط مشفرة
        encoded_patterns = [
            r'atob\(["\']([^"\']+)["\']\)',
            r'base64\(["\']([^"\']+)["\']\)',
            r'decode\(["\']([^"\']+)["\']\)',
        ]
        
        for pattern in encoded_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                try:
                    decoded = base64.b64decode(match).decode('utf-8')
                    if '.m3u8' in decoded or 'http' in decoded:
                        streams.append({
                            'url': decoded,
                            'type': 'HLS (Decoded)',
                            'source': '3rbcafee',
                            'quality': self.detect_quality(decoded)
                        })
                except:
                    pass
        
        return streams
    
    def extract_yallakora_streams(self, html_content):
        """استخراج روابط من موقع يلا كورة"""
        streams = []
        
        print("🔍 تحليل موقع يلا كورة...")
        
        # البحث عن روابط البث المباشر
        patterns = [
            r'data-stream\s*=\s*["\']([^"\']+)["\']',
            r'data-url\s*=\s*["\']([^"\']+)["\']',
            r'href\s*=\s*["\']([^"\']*live[^"\']*)["\']',
            r'src\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if self.is_valid_stream_url(match):
                    streams.append({
                        'url': match,
                        'type': self.detect_stream_type(match),
                        'source': 'yallakora',
                        'quality': self.detect_quality(match)
                    })
        
        return streams
    
    def extract_generic_arabic_streams(self, html_content, source_name="unknown"):
        """استخراج عام للمواقع العربية"""
        streams = []
        
        print(f"🔍 تحليل عام للموقع: {source_name}")
        
        # أنماط شائعة في المواقع العربية
        patterns = [
            # روابط m3u8 مباشرة
            r'https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*',
            
            # روابط في JavaScript
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'src\s*[:=]\s*["\']([^"\']+)["\']',
            r'source\s*[:=]\s*["\']([^"\']+)["\']',
            r'file\s*[:=]\s*["\']([^"\']+)["\']',
            r'url\s*[:=]\s*["\']([^"\']+)["\']',
            
            # روابط RTMP
            r'rtmp://[^\s"\'<>]+',
            r'rtmps://[^\s"\'<>]+',
            
            # روابط بث مباشر أخرى
            r'https?://[^\s"\'<>]+/live/[^\s"\'<>]+',
            r'https?://[^\s"\'<>]+/stream/[^\s"\'<>]+',
            r'https?://[^\s"\'<>]+/playlist\.m3u8[^\s"\'<>]*',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                
                if self.is_valid_stream_url(match):
                    streams.append({
                        'url': match,
                        'type': self.detect_stream_type(match),
                        'source': source_name,
                        'quality': self.detect_quality(match)
                    })
        
        return streams
    
    def extract_from_file(self, file_path):
        """استخراج الروابط من ملف HTML"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            print(f"📁 تحليل الملف: {file_path}")
            
            # تحديد نوع الموقع
            site_type = self.detect_site_type(html_content)
            
            streams = []
            
            if site_type == '3rbcafee':
                streams.extend(self.analyze_3rbcafee_player(html_content))
            elif site_type == 'yallakora':
                streams.extend(self.extract_yallakora_streams(html_content))
            else:
                streams.extend(self.extract_generic_arabic_streams(html_content, site_type))
            
            # إزالة الروابط المكررة
            unique_streams = []
            seen_urls = set()
            
            for stream in streams:
                if stream['url'] not in seen_urls:
                    seen_urls.add(stream['url'])
                    unique_streams.append(stream)
            
            return unique_streams
            
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
            return []
    
    def detect_site_type(self, html_content):
        """تحديد نوع الموقع"""
        content_lower = html_content.lower()
        
        if '3rbcafee' in content_lower or 'عرب كافيه' in content_lower:
            return '3rbcafee'
        elif 'yallakora' in content_lower or 'يلا كورة' in content_lower:
            return 'yallakora'
        elif any(site in content_lower for site in self.arabic_sites):
            for site in self.arabic_sites:
                if site in content_lower:
                    return site.split('.')[0]
        
        return 'generic'
    
    def is_valid_stream_url(self, url):
        """التحقق من صحة رابط البث"""
        if not url or len(url) < 10:
            return False
        
        # تجاهل الروابط غير المفيدة
        ignore_patterns = [
            r'\.js$', r'\.css$', r'\.png$', r'\.jpg$', r'\.gif$',
            r'\.ico$', r'\.svg$', r'\.woff$', r'\.ttf$', r'\.json$',
            r'google', r'facebook', r'twitter', r'analytics',
            r'ads', r'advertisement', r'banner'
        ]
        
        for pattern in ignore_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        # يجب أن يحتوي على مؤشرات البث
        stream_indicators = [
            '.m3u8', '.ts', 'rtmp', 'live', 'stream', 'playlist'
        ]
        
        return any(indicator in url.lower() for indicator in stream_indicators)
    
    def detect_stream_type(self, url):
        """تحديد نوع البث"""
        url_lower = url.lower()
        
        if '.m3u8' in url_lower:
            return 'HLS (m3u8)'
        elif '.ts' in url_lower:
            return 'Transport Stream'
        elif url_lower.startswith('rtmp://'):
            return 'RTMP'
        elif url_lower.startswith('rtmps://'):
            return 'RTMPS'
        elif 'youtube' in url_lower:
            return 'YouTube'
        elif 'live' in url_lower:
            return 'Live Stream'
        else:
            return 'Unknown'
    
    def detect_quality(self, url):
        """تحديد جودة البث"""
        quality_patterns = {
            r'1080p?': '1080p',
            r'720p?': '720p',
            r'480p?': '480p',
            r'360p?': '360p',
            r'240p?': '240p',
            r'hd': 'HD',
            r'sd': 'SD',
            r'low': 'منخفضة',
            r'medium': 'متوسطة',
            r'high': 'عالية'
        }
        
        url_lower = url.lower()
        for pattern, quality in quality_patterns.items():
            if re.search(pattern, url_lower):
                return quality
        
        return 'غير محدد'
    
    def test_stream_url(self, url):
        """اختبار رابط البث"""
        try:
            response = self.session.head(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def save_results(self, streams, filename='arabic_streams.json'):
        """حفظ النتائج"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(streams, f, ensure_ascii=False, indent=2)
            print(f"💾 تم حفظ النتائج في: {filename}")
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {e}")
    
    def create_m3u_playlist(self, streams, filename='arabic_channels.m3u'):
        """إنشاء ملف m3u للقنوات العربية"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('#EXTM3U\n')
                f.write('#PLAYLIST:القنوات العربية المستخرجة\n\n')
                
                for i, stream in enumerate(streams, 1):
                    channel_name = f"قناة {i}"
                    
                    if stream['source'] != 'unknown':
                        channel_name += f" ({stream['source']})"
                    
                    if stream['quality'] != 'غير محدد':
                        channel_name += f" - {stream['quality']}"
                    
                    f.write(f'#EXTINF:-1 tvg-name="{channel_name}" group-title="البث المباشر",{channel_name}\n')
                    f.write(f"{stream['url']}\n\n")
            
            print(f"📺 تم إنشاء ملف M3U: {filename}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف M3U: {e}")

def main():
    print("🎬 أداة استخراج روابط البث من المواقع العربية")
    print("=" * 60)
    
    extractor = ArabicStreamExtractor()
    
    # تحليل ملف url الموجود
    if os.path.exists('url'):
        print("📁 تم العثور على ملف 'url'، جاري التحليل...")
        streams = extractor.extract_from_file('url')
        
        if streams:
            print(f"\n✅ تم العثور على {len(streams)} رابط بث:")
            print("=" * 60)
            
            for i, stream in enumerate(streams, 1):
                print(f"{i}. المصدر: {stream['source']}")
                print(f"   النوع: {stream['type']}")
                print(f"   الجودة: {stream['quality']}")
                print(f"   الرابط: {stream['url']}")
                print("-" * 60)
            
            # حفظ النتائج
            extractor.save_results(streams)
            extractor.create_m3u_playlist(streams)
            
            # اختبار الروابط (اختياري)
            print("\n🔍 هل تريد اختبار الروابط؟ (y/n): ", end="")
            test_choice = input().strip().lower()
            
            if test_choice in ['y', 'yes', 'نعم']:
                print("\n🧪 جاري اختبار الروابط...")
                working_streams = []
                
                for i, stream in enumerate(streams, 1):
                    print(f"اختبار الرابط {i}/{len(streams)}...", end=" ")
                    if extractor.test_stream_url(stream['url']):
                        print("✅ يعمل")
                        working_streams.append(stream)
                    else:
                        print("❌ لا يعمل")
                
                if working_streams:
                    print(f"\n✅ {len(working_streams)} رابط يعمل من أصل {len(streams)}")
                    extractor.save_results(working_streams, 'working_streams.json')
                    extractor.create_m3u_playlist(working_streams, 'working_channels.m3u')
                else:
                    print("\n❌ لا توجد روابط تعمل")
            
        else:
            print("❌ لم يتم العثور على أي روابط بث في الملف")
    
    else:
        print("❌ لم يتم العثور على ملف 'url'")
        print("💡 تأكد من وجود الملف في نفس المجلد")

if __name__ == "__main__":
    main()
