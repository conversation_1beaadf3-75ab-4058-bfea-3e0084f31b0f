#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مخصص لحفظ القنوات
Specialized test for channels saving functionality
"""

import json
import os
import sys
import shutil
from datetime import datetime

def backup_original_data():
    """إنشاء نسخة احتياطية من البيانات الأصلية"""
    if os.path.exists("data.json"):
        backup_name = f"data_original_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        shutil.copy2("data.json", backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def restore_original_data(backup_file):
    """استعادة البيانات الأصلية"""
    if backup_file and os.path.exists(backup_file):
        shutil.copy2(backup_file, "data.json")
        print(f"✅ تم استعادة البيانات الأصلية من: {backup_file}")
        return True
    return False

def test_add_channel():
    """اختبار إضافة قناة جديدة"""
    print("🧪 اختبار إضافة قناة جديدة...")
    
    # قراءة البيانات الحالية
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
    except:
        print("❌ فشل في قراءة ملف البيانات")
        return False
    
    # حفظ عدد القنوات الأصلي
    original_count = len(data.get("channels", []))
    print(f"📊 عدد القنوات الأصلي: {original_count}")
    
    # إضافة قناة اختبار
    test_channel = {
        "name": f"قناة اختبار {datetime.now().strftime('%H%M%S')}",
        "m3u8_url": "https://test.example.com/test_channel.m3u8",
        "activation_base_url": "https://test.example.com/activate.js?",
        "start_counter": 9999
    }
    
    data["channels"].append(test_channel)
    data["last_updated"] = datetime.now().isoformat()
    
    # حفظ البيانات المحدثة
    try:
        with open("data.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print("✅ تم حفظ القناة الجديدة")
    except Exception as e:
        print(f"❌ فشل في حفظ القناة: {e}")
        return False
    
    # التحقق من الحفظ
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            updated_data = json.load(f)
        
        new_count = len(updated_data.get("channels", []))
        print(f"📊 عدد القنوات الجديد: {new_count}")
        
        if new_count == original_count + 1:
            # البحث عن القناة المضافة
            found_channel = None
            for channel in updated_data["channels"]:
                if channel["name"] == test_channel["name"]:
                    found_channel = channel
                    break
            
            if found_channel:
                print(f"✅ تم العثور على القناة المضافة: {found_channel['name']}")
                return True
            else:
                print("❌ لم يتم العثور على القناة المضافة")
                return False
        else:
            print(f"❌ عدد القنوات لم يتغير بشكل صحيح")
            return False
            
    except Exception as e:
        print(f"❌ فشل في التحقق من الحفظ: {e}")
        return False

def test_edit_channel():
    """اختبار تعديل قناة موجودة"""
    print("🧪 اختبار تعديل قناة موجودة...")
    
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
    except:
        print("❌ فشل في قراءة ملف البيانات")
        return False
    
    if not data.get("channels"):
        print("⚠️ لا توجد قنوات للتعديل")
        return False
    
    # تعديل أول قناة
    original_name = data["channels"][0]["name"]
    new_name = f"قناة معدلة {datetime.now().strftime('%H%M%S')}"
    
    data["channels"][0]["name"] = new_name
    data["last_updated"] = datetime.now().isoformat()
    
    # حفظ التعديل
    try:
        with open("data.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✅ تم تعديل القناة: {original_name} → {new_name}")
    except Exception as e:
        print(f"❌ فشل في حفظ التعديل: {e}")
        return False
    
    # التحقق من التعديل
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            updated_data = json.load(f)
        
        if updated_data["channels"][0]["name"] == new_name:
            print("✅ تم حفظ التعديل بنجاح")
            return True
        else:
            print("❌ لم يتم حفظ التعديل")
            return False
            
    except Exception as e:
        print(f"❌ فشل في التحقق من التعديل: {e}")
        return False

def test_delete_channel():
    """اختبار حذف قناة"""
    print("🧪 اختبار حذف قناة...")
    
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
    except:
        print("❌ فشل في قراءة ملف البيانات")
        return False
    
    original_count = len(data.get("channels", []))
    
    if original_count == 0:
        print("⚠️ لا توجد قنوات للحذف")
        return False
    
    # حذف آخر قناة
    deleted_channel = data["channels"].pop()
    data["last_updated"] = datetime.now().isoformat()
    
    print(f"🗑️ حذف القناة: {deleted_channel['name']}")
    
    # حفظ التغيير
    try:
        with open("data.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print("✅ تم حفظ الحذف")
    except Exception as e:
        print(f"❌ فشل في حفظ الحذف: {e}")
        return False
    
    # التحقق من الحذف
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            updated_data = json.load(f)
        
        new_count = len(updated_data.get("channels", []))
        
        if new_count == original_count - 1:
            print(f"✅ تم الحذف بنجاح - العدد: {original_count} → {new_count}")
            return True
        else:
            print(f"❌ لم يتم الحذف بشكل صحيح - العدد: {original_count} → {new_count}")
            return False
            
    except Exception as e:
        print(f"❌ فشل في التحقق من الحذف: {e}")
        return False

def test_file_permissions():
    """اختبار صلاحيات الملف"""
    print("🧪 اختبار صلاحيات الملف...")
    
    test_file = "test_permissions.json"
    test_data = {"test": "data"}
    
    try:
        # اختبار الكتابة
        with open(test_file, "w", encoding="utf-8") as f:
            json.dump(test_data, f)
        
        # اختبار القراءة
        with open(test_file, "r", encoding="utf-8") as f:
            loaded_data = json.load(f)
        
        # اختبار الحذف
        os.remove(test_file)
        
        if loaded_data == test_data:
            print("✅ صلاحيات الملف صحيحة")
            return True
        else:
            print("❌ مشكلة في صلاحيات القراءة")
            return False
            
    except PermissionError:
        print("❌ لا توجد صلاحيات كافية")
        print("💡 جرب تشغيل البرنامج كمدير")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
        return False

def test_json_integrity():
    """اختبار سلامة ملف JSON"""
    print("🧪 اختبار سلامة ملف JSON...")
    
    if not os.path.exists("data.json"):
        print("❌ ملف data.json غير موجود")
        return False
    
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        # التحقق من البنية الأساسية
        required_fields = ["daily_code", "channels"]
        for field in required_fields:
            if field not in data:
                print(f"❌ الحقل المطلوب غير موجود: {field}")
                return False
        
        # التحقق من بنية القنوات
        if not isinstance(data["channels"], list):
            print("❌ القنوات ليست قائمة صحيحة")
            return False
        
        for i, channel in enumerate(data["channels"]):
            if not isinstance(channel, dict):
                print(f"❌ القناة {i+1} ليست كائن صحيح")
                return False
            
            channel_fields = ["name", "m3u8_url", "activation_base_url", "start_counter"]
            for field in channel_fields:
                if field not in channel:
                    print(f"❌ القناة {i+1} تفتقد الحقل: {field}")
                    return False
        
        print(f"✅ ملف JSON سليم - عدد القنوات: {len(data['channels'])}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ خطأ في تنسيق JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص الملف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار مخصص لحفظ القنوات - Live TV Viewer")
    print("=" * 60)
    print()
    
    # إنشاء نسخة احتياطية
    backup_file = backup_original_data()
    
    tests = [
        ("اختبار صلاحيات الملف", test_file_permissions),
        ("اختبار سلامة ملف JSON", test_json_integrity),
        ("اختبار إضافة قناة", test_add_channel),
        ("اختبار تعديل قناة", test_edit_channel),
        ("اختبار حذف قناة", test_delete_channel),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
            failed += 1
        print()
    
    # النتائج
    print("=" * 60)
    print(f"📊 نتائج الاختبارات:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {passed/(passed+failed)*100:.1f}%")
    print()
    
    # استعادة البيانات الأصلية
    if backup_file:
        restore_choice = input("هل تريد استعادة البيانات الأصلية؟ (y/n): ").lower().strip()
        if restore_choice == 'y':
            restore_original_data(backup_file)
        else:
            print(f"💾 النسخة الاحتياطية محفوظة في: {backup_file}")
    
    if failed == 0:
        print("🎉 جميع اختبارات حفظ القنوات نجحت!")
        print("💡 يجب أن تعمل وظيفة حفظ القنوات بشكل صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("💡 تحقق من:")
        print("   - صلاحيات الملفات")
        print("   - مساحة القرص الصلب")
        print("   - برنامج مكافحة الفيروسات")
        print("   - تشغيل البرنامج كمدير")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'✅' if success else '❌'} اضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
