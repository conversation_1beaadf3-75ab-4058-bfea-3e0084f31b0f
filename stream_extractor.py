#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة استخراج روابط البث المباشر وملفات m3u8
Stream URL and M3U8 Extractor Tool
"""

import requests
import re
import json
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
import os

class StreamExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def extract_from_html_file(self, file_path):
        """استخراج الروابط من ملف HTML محلي"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            print(f"📁 تحليل الملف: {file_path}")
            return self.parse_html_content(html_content)
            
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
            return []
    
    def extract_from_url(self, url):
        """استخراج الروابط من رابط مباشر"""
        try:
            print(f"🌐 جاري تحليل الرابط: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            return self.parse_html_content(response.text, base_url=url)
            
        except Exception as e:
            print(f"❌ خطأ في تحميل الرابط: {e}")
            return []
    
    def parse_html_content(self, html_content, base_url=None):
        """تحليل محتوى HTML واستخراج روابط البث"""
        streams = []
        
        # البحث عن روابط m3u8
        m3u8_patterns = [
            r'https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'src\s*[:=]\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'source\s*[:=]\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
        ]
        
        # البحث عن روابط البث المختلفة
        stream_patterns = [
            r'https?://[^\s"\'<>]+/playlist\.m3u8[^\s"\'<>]*',
            r'https?://[^\s"\'<>]+/index\.m3u8[^\s"\'<>]*',
            r'https?://[^\s"\'<>]+\.ts[^\s"\'<>]*',
            r'https?://[^\s"\'<>]+/live/[^\s"\'<>]+',
            r'rtmp://[^\s"\'<>]+',
            r'rtmps://[^\s"\'<>]+',
        ]
        
        all_patterns = m3u8_patterns + stream_patterns
        
        for pattern in all_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match[0] else match[1] if len(match) > 1 else ""
                
                if match and self.is_valid_stream_url(match):
                    if base_url and not match.startswith('http'):
                        match = urljoin(base_url, match)
                    
                    if match not in [s['url'] for s in streams]:
                        stream_type = self.detect_stream_type(match)
                        streams.append({
                            'url': match,
                            'type': stream_type,
                            'quality': self.detect_quality(match)
                        })
        
        # البحث عن روابط في JavaScript
        js_streams = self.extract_from_javascript(html_content, base_url)
        streams.extend(js_streams)
        
        # البحث عن روابط في iframe
        iframe_streams = self.extract_from_iframes(html_content)
        streams.extend(iframe_streams)
        
        return streams
    
    def extract_from_javascript(self, html_content, base_url=None):
        """استخراج الروابط من كود JavaScript"""
        streams = []
        
        # البحث عن متغيرات JavaScript التي تحتوي على روابط
        js_patterns = [
            r'var\s+\w+\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'let\s+\w+\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'const\s+\w+\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'source\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'file\s*:\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if self.is_valid_stream_url(match):
                    if base_url and not match.startswith('http'):
                        match = urljoin(base_url, match)
                    
                    if match not in [s['url'] for s in streams]:
                        streams.append({
                            'url': match,
                            'type': self.detect_stream_type(match),
                            'quality': self.detect_quality(match)
                        })
        
        return streams
    
    def extract_from_iframes(self, html_content):
        """استخراج الروابط من عناصر iframe"""
        streams = []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        iframes = soup.find_all('iframe')
        
        for iframe in iframes:
            src = iframe.get('src')
            if src and self.is_potential_stream_iframe(src):
                try:
                    iframe_streams = self.extract_from_url(src)
                    streams.extend(iframe_streams)
                except:
                    pass
        
        return streams
    
    def is_valid_stream_url(self, url):
        """التحقق من صحة رابط البث"""
        if not url or len(url) < 10:
            return False
        
        # تجاهل الروابط غير المفيدة
        ignore_patterns = [
            r'\.js$', r'\.css$', r'\.png$', r'\.jpg$', r'\.gif$',
            r'\.ico$', r'\.svg$', r'\.woff$', r'\.ttf$',
            r'google', r'facebook', r'twitter', r'analytics',
            r'ads', r'advertisement'
        ]
        
        for pattern in ignore_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        return True
    
    def is_potential_stream_iframe(self, src):
        """التحقق من إمكانية احتواء iframe على بث"""
        stream_indicators = [
            'player', 'stream', 'live', 'video', 'tv', 'channel',
            'embed', 'watch', 'play'
        ]
        
        return any(indicator in src.lower() for indicator in stream_indicators)
    
    def detect_stream_type(self, url):
        """تحديد نوع البث"""
        url_lower = url.lower()
        
        if '.m3u8' in url_lower:
            return 'HLS (m3u8)'
        elif '.ts' in url_lower:
            return 'Transport Stream'
        elif url_lower.startswith('rtmp'):
            return 'RTMP'
        elif url_lower.startswith('rtmps'):
            return 'RTMPS'
        elif 'youtube' in url_lower:
            return 'YouTube'
        elif 'vimeo' in url_lower:
            return 'Vimeo'
        else:
            return 'Unknown'
    
    def detect_quality(self, url):
        """تحديد جودة البث"""
        quality_patterns = {
            r'1080p?': '1080p',
            r'720p?': '720p',
            r'480p?': '480p',
            r'360p?': '360p',
            r'240p?': '240p',
            r'hd': 'HD',
            r'sd': 'SD',
            r'low': 'Low',
            r'medium': 'Medium',
            r'high': 'High'
        }
        
        url_lower = url.lower()
        for pattern, quality in quality_patterns.items():
            if re.search(pattern, url_lower):
                return quality
        
        return 'Unknown'
    
    def save_results(self, streams, filename='extracted_streams.json'):
        """حفظ النتائج في ملف"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(streams, f, ensure_ascii=False, indent=2)
            print(f"💾 تم حفظ النتائج في: {filename}")
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {e}")
    
    def create_m3u_playlist(self, streams, filename='playlist.m3u'):
        """إنشاء ملف m3u للقنوات"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('#EXTM3U\n')
                
                for i, stream in enumerate(streams, 1):
                    channel_name = f"Channel {i} ({stream['type']})"
                    if stream['quality'] != 'Unknown':
                        channel_name += f" - {stream['quality']}"
                    
                    f.write(f'#EXTINF:-1,{channel_name}\n')
                    f.write(f"{stream['url']}\n")
            
            print(f"📺 تم إنشاء ملف M3U: {filename}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف M3U: {e}")

def main():
    print("🎬 أداة استخراج روابط البث المباشر")
    print("=" * 50)
    
    extractor = StreamExtractor()
    
    # تحليل ملف url الموجود
    if os.path.exists('url'):
        print("📁 تم العثور على ملف 'url'، جاري التحليل...")
        streams = extractor.extract_from_html_file('url')
        
        if streams:
            print(f"\n✅ تم العثور على {len(streams)} رابط بث:")
            print("-" * 50)
            
            for i, stream in enumerate(streams, 1):
                print(f"{i}. النوع: {stream['type']}")
                print(f"   الجودة: {stream['quality']}")
                print(f"   الرابط: {stream['url']}")
                print("-" * 50)
            
            # حفظ النتائج
            extractor.save_results(streams)
            extractor.create_m3u_playlist(streams)
            
        else:
            print("❌ لم يتم العثور على أي روابط بث في الملف")
    
    else:
        print("❌ لم يتم العثور على ملف 'url'")
        print("💡 يمكنك إدخال رابط مباشر:")
        
        url = input("أدخل الرابط: ").strip()
        if url:
            streams = extractor.extract_from_url(url)
            
            if streams:
                print(f"\n✅ تم العثور على {len(streams)} رابط بث:")
                for i, stream in enumerate(streams, 1):
                    print(f"{i}. {stream['type']} - {stream['url']}")
                
                extractor.save_results(streams)
                extractor.create_m3u_playlist(streams)
            else:
                print("❌ لم يتم العثور على أي روابط بث")

if __name__ == "__main__":
    main()
