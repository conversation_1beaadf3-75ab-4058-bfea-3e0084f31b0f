#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار البرنامج
Test script for Live TV Viewer
"""

import unittest
import json
import os
import sys
import tempfile
from unittest.mock import patch, MagicMock

# إضافة مسار البرنامج الرئيسي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestLiveTVApp(unittest.TestCase):
    """اختبارات البرنامج الرئيسي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_data = {
            "daily_code": "TEST123",
            "channels": [
                {
                    "name": "قناة اختبار",
                    "m3u8_url": "https://example.com/test.m3u8",
                    "activation_base_url": "https://example.com/activate.js?",
                    "start_counter": 1000
                }
            ],
            "last_updated": "2024-01-15T10:30:00",
            "version": "1.0"
        }
        
    def test_data_structure(self):
        """اختبار بنية البيانات"""
        # التحقق من وجود الحقول المطلوبة
        required_fields = ["daily_code", "channels", "last_updated", "version"]
        for field in required_fields:
            self.assertIn(field, self.test_data)
            
        # التحقق من بنية القنوات
        channel = self.test_data["channels"][0]
        channel_fields = ["name", "m3u8_url", "activation_base_url", "start_counter"]
        for field in channel_fields:
            self.assertIn(field, channel)
            
    def test_json_validity(self):
        """اختبار صحة ملف JSON"""
        # تحويل إلى JSON والعكس
        json_str = json.dumps(self.test_data, ensure_ascii=False)
        parsed_data = json.loads(json_str)
        
        # التحقق من تطابق البيانات
        self.assertEqual(self.test_data, parsed_data)
        
    def test_activation_code_format(self):
        """اختبار تنسيق كود التفعيل"""
        code = self.test_data["daily_code"]
        
        # التحقق من طول الكود
        self.assertGreaterEqual(len(code), 4)
        self.assertLessEqual(len(code), 20)
        
        # التحقق من الأحرف المسموحة
        allowed_chars = set("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
        code_chars = set(code.upper())
        self.assertTrue(code_chars.issubset(allowed_chars))
        
    def test_channel_url_format(self):
        """اختبار تنسيق روابط القنوات"""
        channel = self.test_data["channels"][0]
        
        # التحقق من رابط M3U8
        m3u8_url = channel["m3u8_url"]
        self.assertTrue(m3u8_url.startswith("http"))
        self.assertTrue(m3u8_url.endswith(".m3u8"))
        
        # التحقق من رابط التفعيل
        activation_url = channel["activation_base_url"]
        self.assertTrue(activation_url.startswith("http"))
        
        # التحقق من رقم البداية
        start_counter = channel["start_counter"]
        self.assertIsInstance(start_counter, int)
        self.assertGreater(start_counter, 0)

class TestDataFile(unittest.TestCase):
    """اختبارات ملف البيانات"""
    
    def test_data_file_exists(self):
        """اختبار وجود ملف البيانات"""
        self.assertTrue(os.path.exists("data.json"))
        
    def test_data_file_readable(self):
        """اختبار قابلية قراءة ملف البيانات"""
        try:
            with open("data.json", "r", encoding="utf-8") as f:
                data = json.load(f)
            self.assertIsInstance(data, dict)
        except Exception as e:
            self.fail(f"فشل في قراءة ملف البيانات: {e}")
            
    def test_data_file_structure(self):
        """اختبار بنية ملف البيانات"""
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
            
        # التحقق من الحقول المطلوبة
        required_fields = ["daily_code", "channels"]
        for field in required_fields:
            self.assertIn(field, data)
            
        # التحقق من وجود قنوات
        self.assertIsInstance(data["channels"], list)
        self.assertGreater(len(data["channels"]), 0)

class TestAppFunctions(unittest.TestCase):
    """اختبارات وظائف البرنامج"""
    
    @patch('requests.get')
    def test_data_loading(self, mock_get):
        """اختبار تحميل البيانات"""
        # محاكاة استجابة ناجحة
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "daily_code": "TEST123",
            "channels": []
        }
        mock_get.return_value = mock_response
        
        # اختبار التحميل
        # هنا يمكن إضافة اختبارات أكثر تفصيلاً
        
    def test_code_generation(self):
        """اختبار توليد الأكواد"""
        import hashlib
        from datetime import datetime
        
        # توليد كود يومي
        today = datetime.now().strftime("%Y%m%d")
        expected_code = hashlib.md5(f"LIVETV{today}".encode()).hexdigest()[:8].upper()
        
        # التحقق من تنسيق الكود
        self.assertEqual(len(expected_code), 8)
        self.assertTrue(expected_code.isalnum())

def run_basic_tests():
    """تشغيل الاختبارات الأساسية"""
    print("🧪 تشغيل الاختبارات الأساسية...")
    print("=" * 50)
    
    tests_passed = 0
    tests_failed = 0
    
    # اختبار 1: وجود الملفات المطلوبة
    print("📁 اختبار وجود الملفات...")
    required_files = ["live_tv_app.py", "data.json"]
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"  ✅ {file_name}")
            tests_passed += 1
        else:
            print(f"  ❌ {file_name} غير موجود")
            tests_failed += 1
    
    # اختبار 2: صحة ملف JSON
    print("\n📋 اختبار ملف البيانات...")
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        print("  ✅ ملف JSON صحيح")
        tests_passed += 1
        
        # التحقق من البنية
        if "daily_code" in data and "channels" in data:
            print("  ✅ بنية البيانات صحيحة")
            tests_passed += 1
        else:
            print("  ❌ بنية البيانات غير صحيحة")
            tests_failed += 1
            
    except Exception as e:
        print(f"  ❌ خطأ في ملف JSON: {e}")
        tests_failed += 1
    
    # اختبار 3: استيراد المكتبات
    print("\n📦 اختبار المكتبات...")
    required_modules = ["tkinter", "requests", "json", "threading"]
    for module_name in required_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name}")
            tests_passed += 1
        except ImportError:
            print(f"  ❌ {module_name} غير متوفر")
            tests_failed += 1
    
    # النتائج
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبارات:")
    print(f"  ✅ نجح: {tests_passed}")
    print(f"  ❌ فشل: {tests_failed}")
    print(f"  📈 معدل النجاح: {tests_passed/(tests_passed+tests_failed)*100:.1f}%")
    
    if tests_failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! البرنامج جاهز للتشغيل.")
        return True
    else:
        print(f"\n⚠️ {tests_failed} اختبار فشل. يرجى مراجعة المشاكل أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار برنامج Live TV Viewer")
    print("=" * 50)
    
    # تشغيل الاختبارات الأساسية
    basic_success = run_basic_tests()
    
    print("\n" + "=" * 50)
    
    # تشغيل اختبارات unittest (اختياري)
    run_unittest = input("هل تريد تشغيل الاختبارات المتقدمة؟ (y/n): ").lower().strip()
    
    if run_unittest == 'y':
        print("\n🔬 تشغيل الاختبارات المتقدمة...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n✅ انتهت الاختبارات")
    return basic_success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n❌ اضغط Enter للخروج...")
            sys.exit(1)
        else:
            input("\n✅ اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء الاختبارات")
        sys.exit(1)
