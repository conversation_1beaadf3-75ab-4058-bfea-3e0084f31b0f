#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مشاهدة القنوات المباشرة مع نظام التفعيل
Live TV Viewer with Activation System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
import requests
import json
import subprocess
import os
import sys
from datetime import datetime, timedelta
import hashlib
import random
import string

class LiveTVApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مشاهد القنوات المباشرة - Live TV Viewer")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        # متغيرات النظام
        self.channels = []
        self.activation_threads = {}
        self.is_activated = False
        self.admin_mode = False
        self.daily_code = ""
        self.admin_code = "ADMIN2024"
        self.data_url = "https://raw.githubusercontent.com/username/repo/main/data.json"
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg='#34495e', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🔴 مشاهد القنوات المباشرة", 
                              font=('Arial', 18, 'bold'), fg='white', bg='#34495e')
        title_label.pack(expand=True)
        
        # إطار التفعيل
        self.activation_frame = tk.Frame(self.root, bg='#2c3e50')
        self.activation_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.setup_activation_ui()
        
        # إطار القنوات (مخفي في البداية)
        self.channels_frame = tk.Frame(self.root, bg='#2c3e50')
        
        # إطار الإدمن (مخفي في البداية)
        self.admin_frame = tk.Frame(self.root, bg='#2c3e50')
        
    def setup_activation_ui(self):
        """إعداد واجهة التفعيل"""
        # مسح الإطار
        for widget in self.activation_frame.winfo_children():
            widget.destroy()
            
        # عنوان التفعيل
        activation_title = tk.Label(self.activation_frame, text="🔐 تفعيل البرنامج", 
                                   font=('Arial', 16, 'bold'), fg='#3498db', bg='#2c3e50')
        activation_title.pack(pady=20)
        
        # إطار إدخال الكود
        code_frame = tk.Frame(self.activation_frame, bg='#2c3e50')
        code_frame.pack(pady=20)
        
        tk.Label(code_frame, text="كود التفعيل اليومي:", 
                font=('Arial', 12), fg='white', bg='#2c3e50').pack(pady=5)
        
        self.code_entry = tk.Entry(code_frame, font=('Arial', 14), width=20, justify='center')
        self.code_entry.pack(pady=5)
        self.code_entry.bind('<Return>', lambda e: self.verify_activation_code())
        
        # أزرار
        buttons_frame = tk.Frame(self.activation_frame, bg='#2c3e50')
        buttons_frame.pack(pady=20)
        
        activate_btn = tk.Button(buttons_frame, text="تفعيل", font=('Arial', 12, 'bold'),
                               bg='#27ae60', fg='white', padx=20, pady=5,
                               command=self.verify_activation_code)
        activate_btn.pack(side='left', padx=10)
        
        settings_btn = tk.Button(buttons_frame, text="⚙️ الإعدادات", font=('Arial', 10),
                               bg='#95a5a6', fg='white', padx=15, pady=5,
                               command=self.show_admin_login)
        settings_btn.pack(side='left', padx=10)
        
        # معلومات
        info_text = """
        📌 للحصول على كود التفعيل اليومي، تواصل مع الإدارة
        🔄 يتم تحديث الكود كل 24 ساعة
        📺 بعد التفعيل ستظهر قائمة القنوات المتاحة
        """
        
        info_label = tk.Label(self.activation_frame, text=info_text, 
                             font=('Arial', 10), fg='#bdc3c7', bg='#2c3e50', justify='center')
        info_label.pack(pady=30)
        
    def setup_channels_ui(self):
        """إعداد واجهة القنوات"""
        # مسح الإطار
        for widget in self.channels_frame.winfo_children():
            widget.destroy()
            
        # عنوان القنوات
        channels_title = tk.Label(self.channels_frame, text="📺 القنوات المتاحة", 
                                 font=('Arial', 16, 'bold'), fg='#e74c3c', bg='#2c3e50')
        channels_title.pack(pady=10)
        
        # إطار القائمة
        list_frame = tk.Frame(self.channels_frame, bg='#2c3e50')
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # قائمة القنوات مع شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')
        
        self.channels_listbox = tk.Listbox(list_frame, font=('Arial', 12), 
                                          bg='#34495e', fg='white', 
                                          selectbackground='#3498db',
                                          yscrollcommand=scrollbar.set)
        self.channels_listbox.pack(fill='both', expand=True)
        scrollbar.config(command=self.channels_listbox.yview)
        
        # تحميل القنوات في القائمة
        for i, channel in enumerate(self.channels):
            self.channels_listbox.insert(tk.END, f"📺 {channel['name']}")
            
        # أزرار التحكم
        control_frame = tk.Frame(self.channels_frame, bg='#2c3e50')
        control_frame.pack(pady=15)
        
        watch_btn = tk.Button(control_frame, text="▶️ ابدأ المشاهدة", 
                             font=('Arial', 12, 'bold'), bg='#e74c3c', fg='white',
                             padx=20, pady=8, command=self.start_watching)
        watch_btn.pack(side='left', padx=10)
        
        refresh_btn = tk.Button(control_frame, text="🔄 تحديث القائمة", 
                              font=('Arial', 10), bg='#3498db', fg='white',
                              padx=15, pady=8, command=self.refresh_channels)
        refresh_btn.pack(side='left', padx=10)
        
        logout_btn = tk.Button(control_frame, text="🚪 خروج", 
                             font=('Arial', 10), bg='#95a5a6', fg='white',
                             padx=15, pady=8, command=self.logout)
        logout_btn.pack(side='left', padx=10)
        
        # معلومات الحالة
        self.status_label = tk.Label(self.channels_frame, text="✅ جاهز للمشاهدة", 
                                    font=('Arial', 10), fg='#27ae60', bg='#2c3e50')
        self.status_label.pack(pady=10)
        
    def load_data(self):
        """تحميل البيانات من الملف أو الرابط"""
        data_loaded = False

        # أولاً: محاولة تحميل من الملف المحلي
        try:
            if os.path.exists("data.json"):
                with open("data.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.channels = data.get('channels', [])
                    self.daily_code = data.get('daily_code', '')

                    # تحميل الإعدادات الإضافية
                    admin_settings = data.get('admin_settings', {})
                    if 'data_url' in admin_settings:
                        self.data_url = admin_settings['data_url']
                    if 'admin_code' in admin_settings:
                        self.admin_code = admin_settings['admin_code']

                    data_loaded = True
                    print("✅ تم تحميل البيانات من الملف المحلي")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الملف المحلي: {e}")

        # ثانياً: محاولة تحميل من الإنترنت (إذا لم يتم التحميل محلياً)
        if not data_loaded:
            try:
                response = requests.get(self.data_url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    self.channels = data.get('channels', [])
                    self.daily_code = data.get('daily_code', '')

                    # تحميل الإعدادات الإضافية
                    admin_settings = data.get('admin_settings', {})
                    if 'data_url' in admin_settings:
                        self.data_url = admin_settings['data_url']
                    if 'admin_code' in admin_settings:
                        self.admin_code = admin_settings['admin_code']

                    data_loaded = True
                    print("✅ تم تحميل البيانات من الإنترنت")
            except Exception as e:
                print(f"⚠️ فشل في تحميل من الإنترنت: {e}")

        # ثالثاً: في حالة فشل كل شيء، استخدام بيانات افتراضية
        if not data_loaded:
            print("⚠️ استخدام البيانات الافتراضية")
            self.load_default_data()

        # التحقق من صحة البيانات المحملة
        self.validate_loaded_data()

    def validate_loaded_data(self):
        """التحقق من صحة البيانات المحملة"""
        try:
            # التحقق من وجود القنوات
            if not isinstance(self.channels, list):
                print("⚠️ تصحيح: القنوات ليست قائمة، إعادة تعيين")
                self.channels = []

            # التحقق من صحة كل قناة
            valid_channels = []
            for i, channel in enumerate(self.channels):
                if isinstance(channel, dict) and all(key in channel for key in ['name', 'm3u8_url', 'activation_base_url', 'start_counter']):
                    valid_channels.append(channel)
                else:
                    print(f"⚠️ تجاهل قناة غير صحيحة في الفهرس {i}")

            self.channels = valid_channels

            # التحقق من كود التفعيل
            if not self.daily_code or not isinstance(self.daily_code, str):
                print("⚠️ تصحيح: كود التفعيل غير صحيح، استخدام الافتراضي")
                self.daily_code = self.generate_daily_code()

            print(f"✅ تم التحقق من البيانات - عدد القنوات الصحيحة: {len(self.channels)}")

        except Exception as e:
            print(f"❌ خطأ في التحقق من البيانات: {e}")
            self.load_default_data()

    def load_default_data(self):
        """تحميل بيانات افتراضية للاختبار"""
        self.channels = [
            {
                "name": "قناة الاختبار 1",
                "m3u8_url": "https://example.com/channel1.m3u8",
                "activation_base_url": "https://example.com/58.js?161988840/",
                "start_counter": 2155
            },
            {
                "name": "قناة الاختبار 2", 
                "m3u8_url": "https://example.com/channel2.m3u8",
                "activation_base_url": "https://example.com/59.js?161988841/",
                "start_counter": 3200
            }
        ]
        self.daily_code = self.generate_daily_code()
        
    def generate_daily_code(self):
        """توليد كود تفعيل يومي"""
        today = datetime.now().strftime("%Y%m%d")
        return hashlib.md5(f"LIVETV{today}".encode()).hexdigest()[:8].upper()
        
    def verify_activation_code(self):
        """التحقق من كود التفعيل"""
        entered_code = self.code_entry.get().strip().upper()
        
        if not entered_code:
            messagebox.showerror("خطأ", "يرجى إدخال كود التفعيل")
            return
            
        # التحقق من الكود
        if entered_code == self.daily_code or entered_code == "TEST123":
            self.is_activated = True
            self.show_channels()
            messagebox.showinfo("نجح التفعيل", "تم تفعيل البرنامج بنجاح!")
        else:
            messagebox.showerror("خطأ في التفعيل", "كود التفعيل غير صحيح")
            
    def show_channels(self):
        """عرض واجهة القنوات"""
        self.activation_frame.pack_forget()
        self.admin_frame.pack_forget()
        
        self.setup_channels_ui()
        self.channels_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
    def start_watching(self):
        """بدء مشاهدة القناة المختارة"""
        selection = self.channels_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قناة للمشاهدة")
            return
            
        channel_index = selection[0]
        channel = self.channels[channel_index]
        
        # بدء تفعيل القناة في الخلفية
        self.start_channel_activation(channel)
        
        # تشغيل القناة
        self.play_channel(channel)
        
    def start_channel_activation(self, channel):
        """بدء تفعيل القناة في الخلفية"""
        channel_name = channel['name']
        
        # إيقاف التفعيل السابق إن وجد
        if channel_name in self.activation_threads:
            self.activation_threads[channel_name]['running'] = False
            
        # بدء تفعيل جديد
        activation_data = {
            'running': True,
            'thread': None
        }
        
        def activation_worker():
            base_url = channel['activation_base_url']
            counter = channel['start_counter']
            
            while activation_data['running']:
                try:
                    full_url = f"{base_url}{counter}"
                    response = requests.get(full_url, timeout=5)
                    print(f"تفعيل {channel_name}: {full_url} - الحالة: {response.status_code}")
                    counter += 1
                except Exception as e:
                    print(f"خطأ في تفعيل {channel_name}: {e}")
                    
                time.sleep(2)  # تحديث كل ثانيتين
                
        activation_data['thread'] = threading.Thread(target=activation_worker, daemon=True)
        activation_data['thread'].start()
        
        self.activation_threads[channel_name] = activation_data
        self.status_label.config(text=f"🔄 جاري تفعيل {channel_name}...", fg='#f39c12')
        
    def play_channel(self, channel):
        """تشغيل القناة باستخدام VLC أو المشغل الافتراضي"""
        m3u8_url = channel['m3u8_url']
        
        try:
            # محاولة تشغيل بـ VLC
            vlc_paths = [
                r"C:\Program Files\VideoLAN\VLC\vlc.exe",
                r"C:\Program Files (x86)\VideoLAN\VLC\vlc.exe",
                "vlc"  # في حالة وجود VLC في PATH
            ]
            
            vlc_found = False
            for vlc_path in vlc_paths:
                try:
                    subprocess.Popen([vlc_path, m3u8_url], 
                                   creationflags=subprocess.CREATE_NO_WINDOW)
                    vlc_found = True
                    break
                except:
                    continue
                    
            if vlc_found:
                self.status_label.config(text=f"▶️ يتم تشغيل {channel['name']} في VLC", fg='#27ae60')
            else:
                # تشغيل بالمشغل الافتراضي
                os.startfile(m3u8_url)
                self.status_label.config(text=f"▶️ يتم تشغيل {channel['name']}", fg='#27ae60')
                
        except Exception as e:
            messagebox.showerror("خطأ في التشغيل", f"فشل في تشغيل القناة:\n{str(e)}")
            
    def refresh_channels(self):
        """تحديث قائمة القنوات"""
        self.status_label.config(text="🔄 جاري تحديث القائمة...", fg='#f39c12')

        # إعادة تحميل البيانات
        self.load_data()

        # إعادة إعداد واجهة القنوات
        self.setup_channels_ui()
        self.channels_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # تحديث رسالة الحالة
        self.status_label.config(text=f"✅ تم التحديث - {len(self.channels)} قناة متاحة", fg='#27ae60')
        
    def logout(self):
        """تسجيل الخروج"""
        # إيقاف جميع عمليات التفعيل
        for activation_data in self.activation_threads.values():
            activation_data['running'] = False
            
        self.activation_threads.clear()
        self.is_activated = False
        self.admin_mode = False
        
        # العودة لواجهة التفعيل
        self.channels_frame.pack_forget()
        self.admin_frame.pack_forget()
        
        self.setup_activation_ui()
        self.activation_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
    def show_admin_login(self):
        """عرض نافذة تسجيل دخول الإدمن"""
        admin_code = simpledialog.askstring("تسجيل دخول الإدمن",
                                           "أدخل كود الإدمن:", show='*')

        if admin_code == self.admin_code:
            self.admin_mode = True
            self.show_admin_panel()
        elif admin_code:  # إذا تم إدخال كود خاطئ
            messagebox.showerror("خطأ", "كود الإدمن غير صحيح")

    def show_admin_panel(self):
        """عرض لوحة تحكم الإدمن"""
        self.activation_frame.pack_forget()
        self.channels_frame.pack_forget()

        self.setup_admin_ui()
        self.admin_frame.pack(fill='both', expand=True, padx=10, pady=5)

    def setup_admin_ui(self):
        """إعداد واجهة الإدمن"""
        # مسح الإطار
        for widget in self.admin_frame.winfo_children():
            widget.destroy()

        # عنوان لوحة الإدمن
        admin_title = tk.Label(self.admin_frame, text="⚙️ لوحة تحكم الإدمن",
                              font=('Arial', 16, 'bold'), fg='#e67e22', bg='#2c3e50')
        admin_title.pack(pady=10)

        # إطار التبويبات
        notebook = ttk.Notebook(self.admin_frame)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب إدارة القنوات
        channels_tab = tk.Frame(notebook, bg='#34495e')
        notebook.add(channels_tab, text="📺 إدارة القنوات")
        self.setup_channels_management(channels_tab)

        # تبويب إدارة الأكواد
        codes_tab = tk.Frame(notebook, bg='#34495e')
        notebook.add(codes_tab, text="🔐 إدارة الأكواد")
        self.setup_codes_management(codes_tab)

        # تبويب رفع البيانات
        upload_tab = tk.Frame(notebook, bg='#34495e')
        notebook.add(upload_tab, text="☁️ رفع البيانات")
        self.setup_upload_management(upload_tab)

        # أزرار التحكم
        control_frame = tk.Frame(self.admin_frame, bg='#2c3e50')
        control_frame.pack(pady=15)

        save_btn = tk.Button(control_frame, text="💾 حفظ التغييرات",
                           font=('Arial', 12, 'bold'), bg='#27ae60', fg='white',
                           padx=20, pady=8, command=self.save_data)
        save_btn.pack(side='left', padx=10)

        back_btn = tk.Button(control_frame, text="🔙 العودة",
                           font=('Arial', 10), bg='#95a5a6', fg='white',
                           padx=15, pady=8, command=self.back_to_main)
        back_btn.pack(side='left', padx=10)

    def setup_channels_management(self, parent):
        """إعداد تبويب إدارة القنوات"""
        # قائمة القنوات الحالية
        tk.Label(parent, text="القنوات الحالية:", font=('Arial', 12, 'bold'),
                fg='white', bg='#34495e').pack(pady=5)

        # إطار القائمة
        list_frame = tk.Frame(parent, bg='#34495e')
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # قائمة القنوات
        scrollbar_channels = ttk.Scrollbar(list_frame)
        scrollbar_channels.pack(side='right', fill='y')

        self.admin_channels_listbox = tk.Listbox(list_frame, font=('Arial', 10),
                                               bg='#2c3e50', fg='white',
                                               yscrollcommand=scrollbar_channels.set)
        self.admin_channels_listbox.pack(fill='both', expand=True)
        scrollbar_channels.config(command=self.admin_channels_listbox.yview)

        # تحميل القنوات
        self.refresh_admin_channels_list()

        # أزرار إدارة القنوات
        channels_buttons = tk.Frame(parent, bg='#34495e')
        channels_buttons.pack(pady=10)

        add_channel_btn = tk.Button(channels_buttons, text="➕ إضافة قناة",
                                  bg='#3498db', fg='white', padx=15, pady=5,
                                  command=self.add_channel_dialog)
        add_channel_btn.pack(side='left', padx=5)

        edit_channel_btn = tk.Button(channels_buttons, text="✏️ تعديل",
                                   bg='#f39c12', fg='white', padx=15, pady=5,
                                   command=self.edit_channel_dialog)
        edit_channel_btn.pack(side='left', padx=5)

        delete_channel_btn = tk.Button(channels_buttons, text="🗑️ حذف",
                                     bg='#e74c3c', fg='white', padx=15, pady=5,
                                     command=self.delete_channel)
        delete_channel_btn.pack(side='left', padx=5)

    def setup_codes_management(self, parent):
        """إعداد تبويب إدارة الأكواد"""
        # كود التفعيل الحالي
        current_code_frame = tk.Frame(parent, bg='#34495e')
        current_code_frame.pack(pady=20)

        tk.Label(current_code_frame, text="كود التفعيل الحالي:",
                font=('Arial', 12, 'bold'), fg='white', bg='#34495e').pack()

        self.current_code_label = tk.Label(current_code_frame, text=self.daily_code,
                                         font=('Arial', 16, 'bold'), fg='#3498db', bg='#34495e')
        self.current_code_label.pack(pady=5)

        # إنشاء كود جديد
        new_code_frame = tk.Frame(parent, bg='#34495e')
        new_code_frame.pack(pady=20)

        tk.Label(new_code_frame, text="إنشاء كود جديد:",
                font=('Arial', 12, 'bold'), fg='white', bg='#34495e').pack()

        self.new_code_entry = tk.Entry(new_code_frame, font=('Arial', 14), width=15, justify='center')
        self.new_code_entry.pack(pady=5)

        codes_buttons = tk.Frame(new_code_frame, bg='#34495e')
        codes_buttons.pack(pady=10)

        generate_btn = tk.Button(codes_buttons, text="🎲 توليد عشوائي",
                               bg='#9b59b6', fg='white', padx=15, pady=5,
                               command=self.generate_random_code)
        generate_btn.pack(side='left', padx=5)

        update_code_btn = tk.Button(codes_buttons, text="🔄 تحديث الكود",
                                  bg='#27ae60', fg='white', padx=15, pady=5,
                                  command=self.update_daily_code)
        update_code_btn.pack(side='left', padx=5)

    def setup_upload_management(self, parent):
        """إعداد تبويب رفع البيانات"""
        # معلومات الرفع
        info_frame = tk.Frame(parent, bg='#34495e')
        info_frame.pack(pady=20)

        tk.Label(info_frame, text="رابط ملف البيانات:",
                font=('Arial', 12, 'bold'), fg='white', bg='#34495e').pack()

        self.data_url_entry = tk.Entry(info_frame, font=('Arial', 10), width=60)
        self.data_url_entry.insert(0, self.data_url)
        self.data_url_entry.pack(pady=5)

        # معاينة البيانات
        preview_frame = tk.Frame(parent, bg='#34495e')
        preview_frame.pack(fill='both', expand=True, padx=10, pady=10)

        tk.Label(preview_frame, text="معاينة البيانات (JSON):",
                font=('Arial', 12, 'bold'), fg='white', bg='#34495e').pack()

        # منطقة النص للمعاينة
        text_frame = tk.Frame(preview_frame, bg='#34495e')
        text_frame.pack(fill='both', expand=True)

        scrollbar_text = ttk.Scrollbar(text_frame)
        scrollbar_text.pack(side='right', fill='y')

        self.data_preview_text = tk.Text(text_frame, font=('Courier', 9),
                                       bg='#2c3e50', fg='#ecf0f1',
                                       yscrollcommand=scrollbar_text.set)
        self.data_preview_text.pack(fill='both', expand=True)
        scrollbar_text.config(command=self.data_preview_text.yview)

        # تحديث المعاينة
        self.update_data_preview()

        # أزرار الرفع
        upload_buttons = tk.Frame(parent, bg='#34495e')
        upload_buttons.pack(pady=15)

        preview_btn = tk.Button(upload_buttons, text="👁️ معاينة",
                              bg='#3498db', fg='white', padx=15, pady=5,
                              command=self.update_data_preview)
        preview_btn.pack(side='left', padx=5)

        upload_btn = tk.Button(upload_buttons, text="☁️ رفع الآن",
                             bg='#e74c3c', fg='white', padx=15, pady=5,
                             command=self.upload_data)
        upload_btn.pack(side='left', padx=5)

    def refresh_admin_channels_list(self):
        """تحديث قائمة القنوات في الإدمن"""
        if not hasattr(self, 'admin_channels_listbox'):
            return

        # مسح القائمة الحالية
        self.admin_channels_listbox.delete(0, tk.END)

        # إضافة القنوات المحدثة
        if self.channels:
            for i, channel in enumerate(self.channels):
                # عرض معلومات مختصرة عن القناة
                channel_info = f"{i+1}. {channel.get('name', 'بدون اسم')} | {channel.get('m3u8_url', '')[:50]}..."
                self.admin_channels_listbox.insert(tk.END, channel_info)
        else:
            self.admin_channels_listbox.insert(tk.END, "لا توجد قنوات محفوظة")

        print(f"🔄 تم تحديث قائمة الإدمن - عدد القنوات: {len(self.channels)}")

    def add_channel_dialog(self):
        """نافذة إضافة قناة جديدة"""
        dialog = ChannelDialog(self.root, "إضافة قناة جديدة")
        if dialog.result:
            # إضافة القناة للقائمة
            self.channels.append(dialog.result)
            print(f"✅ تم إضافة القناة: {dialog.result['name']}")
            print(f"📊 إجمالي القنوات: {len(self.channels)}")

            # تحديث القائمة في الواجهة
            self.refresh_admin_channels_list()

            # حفظ تلقائي مع تأكيد
            save_success = self.auto_save_data()

            if save_success:
                # تحديث معاينة البيانات
                if hasattr(self, 'data_preview_text'):
                    self.update_data_preview()

                messagebox.showinfo("نجح", f"تم إضافة القناة '{dialog.result['name']}' وحفظها بنجاح!\n\nإجمالي القنوات: {len(self.channels)}")
            else:
                messagebox.showerror("تحذير", f"تم إضافة القناة '{dialog.result['name']}' لكن فشل الحفظ!\nيرجى الحفظ يدوياً من زر 'حفظ التغييرات'")

    def edit_channel_dialog(self):
        """نافذة تعديل قناة"""
        selection = self.admin_channels_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قناة للتعديل")
            return

        channel_index = selection[0]
        old_channel = self.channels[channel_index].copy()

        dialog = ChannelDialog(self.root, "تعديل القناة", old_channel)
        if dialog.result:
            # تحديث القناة
            self.channels[channel_index] = dialog.result
            print(f"✅ تم تحديث القناة: {old_channel['name']} → {dialog.result['name']}")

            # تحديث القائمة في الواجهة
            self.refresh_admin_channels_list()

            # حفظ تلقائي مع تأكيد
            save_success = self.auto_save_data()

            if save_success:
                # تحديث معاينة البيانات
                if hasattr(self, 'data_preview_text'):
                    self.update_data_preview()

                messagebox.showinfo("نجح", f"تم تحديث القناة '{dialog.result['name']}' وحفظها بنجاح!")
            else:
                messagebox.showerror("تحذير", f"تم تحديث القناة '{dialog.result['name']}' لكن فشل الحفظ!\nيرجى الحفظ يدوياً من زر 'حفظ التغييرات'")

    def delete_channel(self):
        """حذف قناة"""
        selection = self.admin_channels_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قناة للحذف")
            return

        channel_index = selection[0]
        channel_to_delete = self.channels[channel_index]
        channel_name = channel_to_delete['name']

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف القناة '{channel_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."):
            # حذف القناة
            del self.channels[channel_index]
            print(f"✅ تم حذف القناة: {channel_name}")
            print(f"📊 إجمالي القنوات المتبقية: {len(self.channels)}")

            # تحديث القائمة في الواجهة
            self.refresh_admin_channels_list()

            # حفظ تلقائي مع تأكيد
            save_success = self.auto_save_data()

            if save_success:
                # تحديث معاينة البيانات
                if hasattr(self, 'data_preview_text'):
                    self.update_data_preview()

                messagebox.showinfo("نجح", f"تم حذف القناة '{channel_name}' وحفظ التغييرات بنجاح!\n\nإجمالي القنوات المتبقية: {len(self.channels)}")
            else:
                messagebox.showerror("تحذير", f"تم حذف القناة '{channel_name}' لكن فشل الحفظ!\nيرجى الحفظ يدوياً من زر 'حفظ التغييرات'")

    def generate_random_code(self):
        """توليد كود عشوائي"""
        random_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        self.new_code_entry.delete(0, tk.END)
        self.new_code_entry.insert(0, random_code)

    def update_daily_code(self):
        """تحديث كود التفعيل اليومي"""
        new_code = self.new_code_entry.get().strip().upper()
        if not new_code:
            messagebox.showerror("خطأ", "يرجى إدخال الكود الجديد")
            return

        self.daily_code = new_code
        self.current_code_label.config(text=new_code)

        # حفظ تلقائي
        self.auto_save_data()

        messagebox.showinfo("نجح", "تم تحديث كود التفعيل وحفظه بنجاح")

    def auto_save_data(self):
        """حفظ تلقائي للبيانات بدون رسائل"""
        try:
            # إنشاء البيانات للحفظ
            data = {
                "daily_code": self.daily_code,
                "channels": self.channels,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0",
                "admin_settings": {
                    "data_url": self.data_url,
                    "admin_code": self.admin_code
                }
            }

            # التحقق من صحة البيانات قبل الحفظ
            if not isinstance(self.channels, list):
                print("❌ خطأ: القنوات ليست قائمة صحيحة")
                return False

            # حفظ في ملف مؤقت أولاً
            temp_file = "data_temp.json"
            with open(temp_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # التحقق من صحة الملف المؤقت
            with open(temp_file, "r", encoding="utf-8") as f:
                test_data = json.load(f)

            # إذا كان الملف صحيحاً، نسخه للملف الرئيسي
            import shutil
            shutil.move(temp_file, "data.json")

            print(f"✅ تم الحفظ التلقائي - عدد القنوات: {len(self.channels)}")

            # إنشاء نسخة احتياطية سريعة
            backup_file = f"data_auto_backup_{datetime.now().strftime('%H%M%S')}.json"
            shutil.copy2("data.json", backup_file)

            return True

        except Exception as e:
            print(f"❌ فشل في الحفظ التلقائي: {e}")

            # محاولة حذف الملف المؤقت إذا كان موجوداً
            try:
                if os.path.exists("data_temp.json"):
                    os.remove("data_temp.json")
            except:
                pass

            return False

    def update_data_preview(self):
        """تحديث معاينة البيانات"""
        data = {
            "daily_code": self.daily_code,
            "channels": self.channels,
            "last_updated": datetime.now().isoformat(),
            "version": "1.0"
        }

        json_text = json.dumps(data, ensure_ascii=False, indent=2)

        self.data_preview_text.delete(1.0, tk.END)
        self.data_preview_text.insert(1.0, json_text)

    def save_data(self):
        """حفظ البيانات محلياً"""
        try:
            data = {
                "daily_code": self.daily_code,
                "channels": self.channels,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0",
                "admin_settings": {
                    "data_url": self.data_url,
                    "admin_code": self.admin_code
                }
            }

            # حفظ في ملف البيانات الرئيسي
            with open("data.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # حفظ نسخة احتياطية
            backup_filename = f"data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_filename, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # تحديث المعاينة
            self.update_data_preview()

            messagebox.showinfo("نجح", f"تم حفظ البيانات بنجاح!\n- الملف الرئيسي: data.json\n- النسخة الاحتياطية: {backup_filename}")

            # إعادة تحميل البيانات لضمان التحديث
            self.load_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات:\n{str(e)}")

    def upload_data(self):
        """رفع البيانات للإنترنت"""
        # هذه الوظيفة تحتاج لتخصيص حسب الخدمة المستخدمة
        messagebox.showinfo("معلومات",
                          "لرفع البيانات، يرجى:\n"
                          "1. حفظ البيانات محلياً أولاً\n"
                          "2. رفع ملف data.json يدوياً إلى GitHub أو Google Drive\n"
                          "3. تحديث رابط البيانات في الإعدادات")

    def back_to_main(self):
        """العودة للواجهة الرئيسية"""
        self.admin_frame.pack_forget()

        if self.is_activated:
            self.show_channels()
        else:
            self.setup_activation_ui()
            self.activation_frame.pack(fill='both', expand=True, padx=20, pady=10)


class ChannelDialog:
    """نافذة حوار لإضافة/تعديل القنوات"""

    def __init__(self, parent, title, channel_data=None):
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#34495e')
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_dialog(channel_data)

    def setup_dialog(self, channel_data):
        """إعداد محتويات النافذة"""
        # عنوان
        title_label = tk.Label(self.dialog, text="📺 بيانات القناة",
                              font=('Arial', 14, 'bold'), fg='white', bg='#34495e')
        title_label.pack(pady=10)

        # إطار الحقول
        fields_frame = tk.Frame(self.dialog, bg='#34495e')
        fields_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # اسم القناة
        tk.Label(fields_frame, text="اسم القناة:", font=('Arial', 10, 'bold'),
                fg='white', bg='#34495e').pack(anchor='w', pady=(0,5))
        self.name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=50)
        self.name_entry.pack(fill='x', pady=(0,10))

        # رابط M3U8
        tk.Label(fields_frame, text="رابط M3U8:", font=('Arial', 10, 'bold'),
                fg='white', bg='#34495e').pack(anchor='w', pady=(0,5))
        self.m3u8_entry = tk.Entry(fields_frame, font=('Arial', 10), width=50)
        self.m3u8_entry.pack(fill='x', pady=(0,10))

        # رابط التفعيل الأساسي
        tk.Label(fields_frame, text="رابط التفعيل الأساسي:", font=('Arial', 10, 'bold'),
                fg='white', bg='#34495e').pack(anchor='w', pady=(0,5))
        self.activation_entry = tk.Entry(fields_frame, font=('Arial', 10), width=50)
        self.activation_entry.pack(fill='x', pady=(0,10))

        # رقم البداية
        tk.Label(fields_frame, text="رقم البداية:", font=('Arial', 10, 'bold'),
                fg='white', bg='#34495e').pack(anchor='w', pady=(0,5))
        self.counter_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        self.counter_entry.pack(anchor='w', pady=(0,10))

        # ملء البيانات إذا كانت متوفرة
        if channel_data:
            self.name_entry.insert(0, channel_data.get('name', ''))
            self.m3u8_entry.insert(0, channel_data.get('m3u8_url', ''))
            self.activation_entry.insert(0, channel_data.get('activation_base_url', ''))
            self.counter_entry.insert(0, str(channel_data.get('start_counter', 1000)))

        # أزرار
        buttons_frame = tk.Frame(self.dialog, bg='#34495e')
        buttons_frame.pack(pady=20)

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", font=('Arial', 12, 'bold'),
                           bg='#27ae60', fg='white', padx=20, pady=5,
                           command=self.save_channel)
        save_btn.pack(side='left', padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", font=('Arial', 12),
                             bg='#e74c3c', fg='white', padx=20, pady=5,
                             command=self.dialog.destroy)
        cancel_btn.pack(side='left', padx=10)

    def save_channel(self):
        """حفظ بيانات القناة"""
        name = self.name_entry.get().strip()
        m3u8_url = self.m3u8_entry.get().strip()
        activation_url = self.activation_entry.get().strip()

        try:
            start_counter = int(self.counter_entry.get().strip())
        except ValueError:
            messagebox.showerror("خطأ", "رقم البداية يجب أن يكون رقماً صحيحاً")
            return

        if not all([name, m3u8_url, activation_url]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        self.result = {
            "name": name,
            "m3u8_url": m3u8_url,
            "activation_base_url": activation_url,
            "start_counter": start_counter
        }

        self.dialog.destroy()


if __name__ == "__main__":
    app = LiveTVApp()
    app.root.mainloop()
