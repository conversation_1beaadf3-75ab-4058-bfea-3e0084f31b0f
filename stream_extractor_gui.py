#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة استخراج روابط البث مع واجهة رسومية
Stream Extractor with GUI Interface
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import re
import json
import base64
from urllib.parse import urljoin, urlparse, unquote
from bs4 import BeautifulSoup
import threading
import os
from datetime import datetime

class StreamExtractorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("أداة استخراج روابط البث المباشر - Stream Extractor")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات
        self.streams = []
        self.is_extracting = False
        
        # إعداد الجلسة
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🎬 أداة استخراج روابط البث المباشر", 
                              font=('Arial', 16, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.root, text="مصدر البيانات", font=('Arial', 10, 'bold'), 
                                   bg='#f0f0f0', fg='#2c3e50')
        input_frame.pack(fill='x', padx=10, pady=5)
        
        # خيارات الإدخال
        self.input_type = tk.StringVar(value="file")
        
        tk.Radiobutton(input_frame, text="ملف HTML محلي", variable=self.input_type, 
                      value="file", font=('Arial', 9), bg='#f0f0f0').pack(anchor='w', padx=10, pady=2)
        
        tk.Radiobutton(input_frame, text="رابط مباشر", variable=self.input_type, 
                      value="url", font=('Arial', 9), bg='#f0f0f0').pack(anchor='w', padx=10, pady=2)
        
        # إطار اختيار الملف
        file_frame = tk.Frame(input_frame, bg='#f0f0f0')
        file_frame.pack(fill='x', padx=10, pady=5)
        
        self.file_path = tk.StringVar()
        tk.Entry(file_frame, textvariable=self.file_path, font=('Arial', 9), width=60).pack(side='left', padx=(0, 5))
        tk.Button(file_frame, text="اختر ملف", command=self.browse_file, 
                 bg='#3498db', fg='white', font=('Arial', 9)).pack(side='right')
        
        # إطار الرابط
        url_frame = tk.Frame(input_frame, bg='#f0f0f0')
        url_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(url_frame, text="الرابط:", font=('Arial', 9), bg='#f0f0f0').pack(side='left')
        self.url_entry = tk.Entry(url_frame, font=('Arial', 9), width=70)
        self.url_entry.pack(side='left', padx=(5, 0), fill='x', expand=True)
        
        # أزرار التحكم
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.extract_btn = tk.Button(control_frame, text="🔍 استخراج الروابط", 
                                    command=self.start_extraction, bg='#27ae60', fg='white', 
                                    font=('Arial', 10, 'bold'), height=2)
        self.extract_btn.pack(side='left', padx=(0, 5))
        
        tk.Button(control_frame, text="💾 حفظ النتائج", command=self.save_results, 
                 bg='#f39c12', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        
        tk.Button(control_frame, text="📺 إنشاء M3U", command=self.create_m3u, 
                 bg='#9b59b6', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        
        tk.Button(control_frame, text="🧪 اختبار الروابط", command=self.test_streams, 
                 bg='#e74c3c', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        
        tk.Button(control_frame, text="🗑️ مسح النتائج", command=self.clear_results, 
                 bg='#95a5a6', fg='white', font=('Arial', 10)).pack(side='right')
        
        # شريط التقدم
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill='x', padx=10, pady=5)
        
        # منطقة النتائج
        results_frame = tk.LabelFrame(self.root, text="النتائج", font=('Arial', 10, 'bold'), 
                                     bg='#f0f0f0', fg='#2c3e50')
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # جدول النتائج
        columns = ('الرقم', 'المصدر', 'النوع', 'الجودة', 'الرابط')
        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            if col == 'الرابط':
                self.tree.column(col, width=400)
            else:
                self.tree.column(col, width=80)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # منطقة السجل
        log_frame = tk.LabelFrame(self.root, text="سجل العمليات", font=('Arial', 9, 'bold'), 
                                 bg='#f0f0f0', fg='#2c3e50')
        log_frame.pack(fill='x', padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=6, font=('Consolas', 8))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز للاستخدام")
        status_bar = tk.Label(self.root, textvariable=self.status_var, relief='sunken', 
                             anchor='w', bg='#ecf0f1', font=('Arial', 8))
        status_bar.pack(fill='x', side='bottom')
        
        # تحميل ملف url إذا كان موجوداً
        if os.path.exists('url'):
            self.file_path.set('url')
            self.log("تم العثور على ملف 'url' وتحميله تلقائياً")
    
    def log(self, message):
        """إضافة رسالة إلى السجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def browse_file(self):
        """اختيار ملف HTML"""
        filename = filedialog.askopenfilename(
            title="اختر ملف HTML",
            filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
        )
        if filename:
            self.file_path.set(filename)
            self.log(f"تم اختيار الملف: {os.path.basename(filename)}")
    
    def start_extraction(self):
        """بدء عملية الاستخراج"""
        if self.is_extracting:
            return
        
        # التحقق من الإدخال
        if self.input_type.get() == "file":
            if not self.file_path.get() or not os.path.exists(self.file_path.get()):
                messagebox.showerror("خطأ", "يرجى اختيار ملف HTML صحيح")
                return
        else:
            if not self.url_entry.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال رابط صحيح")
                return
        
        # بدء الاستخراج في خيط منفصل
        self.is_extracting = True
        self.extract_btn.config(state='disabled', text="جاري الاستخراج...")
        self.progress.start()
        
        thread = threading.Thread(target=self.extract_streams)
        thread.daemon = True
        thread.start()
    
    def extract_streams(self):
        """استخراج الروابط"""
        try:
            self.log("بدء عملية استخراج الروابط...")
            self.status_var.set("جاري الاستخراج...")
            
            if self.input_type.get() == "file":
                streams = self.extract_from_file(self.file_path.get())
            else:
                streams = self.extract_from_url(self.url_entry.get().strip())
            
            # تحديث الواجهة في الخيط الرئيسي
            self.root.after(0, self.update_results, streams)
            
        except Exception as e:
            self.root.after(0, self.extraction_error, str(e))
    
    def update_results(self, streams):
        """تحديث النتائج في الواجهة"""
        self.streams = streams
        self.is_extracting = False
        self.extract_btn.config(state='normal', text="🔍 استخراج الروابط")
        self.progress.stop()
        
        # مسح النتائج السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة النتائج الجديدة
        for i, stream in enumerate(streams, 1):
            self.tree.insert('', 'end', values=(
                i,
                stream.get('source', 'غير محدد'),
                stream.get('type', 'غير محدد'),
                stream.get('quality', 'غير محدد'),
                stream['url']
            ))
        
        count = len(streams)
        self.log(f"تم العثور على {count} رابط بث")
        self.status_var.set(f"تم العثور على {count} رابط")
        
        if count == 0:
            messagebox.showinfo("النتيجة", "لم يتم العثور على أي روابط بث")
        else:
            messagebox.showinfo("النتيجة", f"تم العثور على {count} رابط بث")
    
    def extraction_error(self, error_msg):
        """معالجة خطأ الاستخراج"""
        self.is_extracting = False
        self.extract_btn.config(state='normal', text="🔍 استخراج الروابط")
        self.progress.stop()
        
        self.log(f"خطأ في الاستخراج: {error_msg}")
        self.status_var.set("حدث خطأ في الاستخراج")
        messagebox.showerror("خطأ", f"حدث خطأ في الاستخراج:\n{error_msg}")
    
    def extract_from_file(self, file_path):
        """استخراج من ملف"""
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        self.log(f"تحليل الملف: {os.path.basename(file_path)}")
        return self.parse_html_content(html_content)
    
    def extract_from_url(self, url):
        """استخراج من رابط"""
        self.log(f"تحميل الرابط: {url}")
        response = self.session.get(url, timeout=10)
        response.raise_for_status()
        
        return self.parse_html_content(response.text, base_url=url)
    
    def parse_html_content(self, html_content, base_url=None):
        """تحليل محتوى HTML"""
        streams = []
        
        # تحديد نوع الموقع
        site_type = self.detect_site_type(html_content)
        self.log(f"نوع الموقع المكتشف: {site_type}")
        
        # أنماط البحث المختلفة
        patterns = [
            # روابط m3u8 مباشرة
            r'https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*',
            
            # روابط في JavaScript
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'src\s*[:=]\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
            r'source\s*[:=]\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
            r'file\s*[:=]\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
            r'url\s*[:=]\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
            
            # روابط RTMP
            r'rtmp://[^\s"\'<>]+',
            r'rtmps://[^\s"\'<>]+',
            
            # روابط بث أخرى
            r'https?://[^\s"\'<>]+/live/[^\s"\'<>]+',
            r'https?://[^\s"\'<>]+/stream/[^\s"\'<>]+',
            r'https?://[^\s"\'<>]+/playlist\.m3u8[^\s"\'<>]*',
        ]
        
        found_urls = set()
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                
                if self.is_valid_stream_url(match) and match not in found_urls:
                    found_urls.add(match)
                    
                    if base_url and not match.startswith('http'):
                        match = urljoin(base_url, match)
                    
                    streams.append({
                        'url': match,
                        'type': self.detect_stream_type(match),
                        'source': site_type,
                        'quality': self.detect_quality(match)
                    })
        
        # البحث عن روابط مشفرة
        self.extract_encoded_streams(html_content, streams, site_type)
        
        return streams
    
    def extract_encoded_streams(self, html_content, streams, site_type):
        """استخراج الروابط المشفرة"""
        encoded_patterns = [
            r'atob\(["\']([^"\']+)["\']\)',
            r'base64\(["\']([^"\']+)["\']\)',
            r'decode\(["\']([^"\']+)["\']\)',
        ]
        
        for pattern in encoded_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                try:
                    decoded = base64.b64decode(match).decode('utf-8')
                    if '.m3u8' in decoded or 'rtmp' in decoded:
                        streams.append({
                            'url': decoded,
                            'type': 'مشفر - ' + self.detect_stream_type(decoded),
                            'source': site_type,
                            'quality': self.detect_quality(decoded)
                        })
                except:
                    pass
    
    def detect_site_type(self, html_content):
        """تحديد نوع الموقع"""
        content_lower = html_content.lower()
        
        if '3rbcafee' in content_lower or 'عرب كافيه' in content_lower:
            return 'عرب كافيه'
        elif 'yallakora' in content_lower or 'يلا كورة' in content_lower:
            return 'يلا كورة'
        elif 'koralive' in content_lower:
            return 'كورة لايف'
        elif 'yalla-shoot' in content_lower:
            return 'يلا شوت'
        else:
            return 'عام'
    
    def is_valid_stream_url(self, url):
        """التحقق من صحة رابط البث"""
        if not url or len(url) < 10:
            return False
        
        # تجاهل الروابط غير المفيدة
        ignore_patterns = [
            r'\.js$', r'\.css$', r'\.png$', r'\.jpg$', r'\.gif$',
            r'\.ico$', r'\.svg$', r'\.woff$', r'\.ttf$', r'\.json$',
            r'google', r'facebook', r'twitter', r'analytics',
            r'ads', r'advertisement', r'banner'
        ]
        
        for pattern in ignore_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        # يجب أن يحتوي على مؤشرات البث
        stream_indicators = [
            '.m3u8', '.ts', 'rtmp', 'live', 'stream', 'playlist'
        ]
        
        return any(indicator in url.lower() for indicator in stream_indicators)
    
    def detect_stream_type(self, url):
        """تحديد نوع البث"""
        url_lower = url.lower()
        
        if '.m3u8' in url_lower:
            return 'HLS (m3u8)'
        elif '.ts' in url_lower:
            return 'Transport Stream'
        elif url_lower.startswith('rtmp://'):
            return 'RTMP'
        elif url_lower.startswith('rtmps://'):
            return 'RTMPS'
        elif 'youtube' in url_lower:
            return 'YouTube'
        elif 'live' in url_lower:
            return 'بث مباشر'
        else:
            return 'غير محدد'
    
    def detect_quality(self, url):
        """تحديد جودة البث"""
        quality_patterns = {
            r'1080p?': '1080p',
            r'720p?': '720p',
            r'480p?': '480p',
            r'360p?': '360p',
            r'240p?': '240p',
            r'hd': 'HD',
            r'sd': 'SD',
            r'low': 'منخفضة',
            r'medium': 'متوسطة',
            r'high': 'عالية'
        }
        
        url_lower = url.lower()
        for pattern, quality in quality_patterns.items():
            if re.search(pattern, url_lower):
                return quality
        
        return 'غير محدد'
    
    def save_results(self):
        """حفظ النتائج"""
        if not self.streams:
            messagebox.showwarning("تحذير", "لا توجد نتائج لحفظها")
            return
        
        filename = filedialog.asksaveasfilename(
            title="حفظ النتائج",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.streams, f, ensure_ascii=False, indent=2)
                
                self.log(f"تم حفظ النتائج في: {os.path.basename(filename)}")
                messagebox.showinfo("نجح", f"تم حفظ {len(self.streams)} رابط بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف:\n{e}")
    
    def create_m3u(self):
        """إنشاء ملف M3U"""
        if not self.streams:
            messagebox.showwarning("تحذير", "لا توجد روابط لإنشاء ملف M3U")
            return
        
        filename = filedialog.asksaveasfilename(
            title="إنشاء ملف M3U",
            defaultextension=".m3u",
            filetypes=[("M3U files", "*.m3u"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write('#EXTM3U\n')
                    f.write('#PLAYLIST:القنوات المستخرجة\n\n')
                    
                    for i, stream in enumerate(self.streams, 1):
                        channel_name = f"قناة {i}"
                        
                        if stream.get('source') != 'عام':
                            channel_name += f" ({stream['source']})"
                        
                        if stream.get('quality') != 'غير محدد':
                            channel_name += f" - {stream['quality']}"
                        
                        f.write(f'#EXTINF:-1 tvg-name="{channel_name}" group-title="البث المباشر",{channel_name}\n')
                        f.write(f"{stream['url']}\n\n")
                
                self.log(f"تم إنشاء ملف M3U: {os.path.basename(filename)}")
                messagebox.showinfo("نجح", f"تم إنشاء ملف M3U بـ {len(self.streams)} قناة")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء ملف M3U:\n{e}")
    
    def test_streams(self):
        """اختبار الروابط"""
        if not self.streams:
            messagebox.showwarning("تحذير", "لا توجد روابط للاختبار")
            return
        
        # بدء الاختبار في خيط منفصل
        self.log("بدء اختبار الروابط...")
        self.status_var.set("جاري اختبار الروابط...")
        self.progress.start()
        
        thread = threading.Thread(target=self.test_streams_thread)
        thread.daemon = True
        thread.start()
    
    def test_streams_thread(self):
        """اختبار الروابط في خيط منفصل"""
        working_count = 0
        total_count = len(self.streams)
        
        for i, stream in enumerate(self.streams, 1):
            try:
                self.root.after(0, lambda i=i, total=total_count: 
                               self.status_var.set(f"اختبار الرابط {i}/{total}..."))
                
                response = self.session.head(stream['url'], timeout=5)
                if response.status_code == 200:
                    working_count += 1
                    status = "✅ يعمل"
                else:
                    status = "❌ لا يعمل"
                
            except:
                status = "❌ لا يعمل"
            
            self.root.after(0, lambda msg=f"الرابط {i}: {status}": self.log(msg))
        
        # تحديث النتيجة النهائية
        self.root.after(0, self.test_complete, working_count, total_count)
    
    def test_complete(self, working_count, total_count):
        """إنهاء اختبار الروابط"""
        self.progress.stop()
        self.status_var.set(f"{working_count} من {total_count} رابط يعمل")
        
        message = f"نتيجة الاختبار:\n{working_count} رابط يعمل من أصل {total_count}"
        messagebox.showinfo("نتيجة الاختبار", message)
        self.log(f"انتهى الاختبار: {working_count}/{total_count} رابط يعمل")
    
    def clear_results(self):
        """مسح النتائج"""
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.streams = []
        self.log("تم مسح جميع النتائج")
        self.status_var.set("تم مسح النتائج")

def main():
    root = tk.Tk()
    app = StreamExtractorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
