<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' dir='rtl' lang='ar' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    
    <b:if cond='data:view.isHomepage'>
        <title>راية كافية - تطبيق مشاهدة المباريات المباشرة</title>
        <meta content='حمل تطبيق راية كافية لمشاهدة المباريات المباشرة مجاناً. أفضل تطبيق لمتابعة كرة القدم والقنوات الرياضية بجودة عالية' name='description'/>
    <b:else/>
        <title><data:view.title/></title>
        <b:if cond='data:view.description'>
            <meta expr:content='data:view.description' name='description'/>
        </b:if>
    </b:if>
    
    <meta content='راية كافية, مشاهدة المباريات, بث مباشر, كرة القدم, قنوات رياضية' name='keywords'/>
    
    <!-- Open Graph Meta Tags -->
    <meta content='راية كافية - تطبيق مشاهدة المباريات المباشرة' property='og:title'/>
    <meta content='حمل تطبيق راية كافية لمشاهدة المباريات المباشرة مجاناً' property='og:description'/>
    <meta content='website' property='og:type'/>
    <meta content='https://via.placeholder.com/1200x630/1a73e8/ffffff?text=راية+كافية' property='og:image'/>
    
    <!-- Favicon -->
    <b:skin><![CDATA[
    /* Blogger Template Variables */
    <Variable name="keycolor" description="اللون الأساسي - Primary Color" type="color" default="#6c7b7f" value="#6c7b7f"/>
    <Variable name="secondarycolor" description="اللون الثانوي - Secondary Color" type="color" default="#95a5a6" value="#95a5a6"/>
    <Variable name="accentcolor" description="لون التمييز - Accent Color" type="color" default="#3498db" value="#3498db"/>
    <Variable name="backgroundcolor" description="لون الخلفية - Background Color" type="color" default="#f8f9fa" value="#f8f9fa"/>
    <Variable name="textcolor" description="لون النص - Text Color" type="color" default="#2c3e50" value="#2c3e50"/>
    <Variable name="cardcolor" description="لون البطاقات - Card Color" type="color" default="#ffffff" value="#ffffff"/>
    <Variable name="body.font" description="الخط الأساسي - Main Font" type="font" default="normal normal 400 16px Cairo, Arial, sans-serif" value="normal normal 400 16px Cairo, Arial, sans-serif"/>
    <Variable name="heading.font" description="خط العناوين - Heading Font" type="font" default="normal normal 700 24px Cairo, Arial, sans-serif" value="normal normal 700 24px Cairo, Arial, sans-serif"/>
    <Variable name="borderradius" description="انحناء الحواف - Border Radius" type="length" default="15px" min="0px" max="50px" value="15px"/>
    <Variable name="shadowintensity" description="شدة الظلال - Shadow Intensity" type="string" default="0.1" value="0.1"/>
    ]]></b:skin>
    
    <!-- Google Fonts -->
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&amp;display=swap' rel='stylesheet'/>
    
    <!-- Font Awesome -->
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    
    <!-- Bootstrap CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    
    <!-- AOS Animation Library -->
    <link href='https://unpkg.com/aos@2.3.1/dist/aos.css' rel='stylesheet'/>
    
    <!-- Custom CSS -->
    <b:template-skin>
    <![CDATA[
        :root {
            --primary-color: $(keycolor);
            --secondary-color: $(secondarycolor);
            --accent-color: $(accentcolor);
            --background-color: $(backgroundcolor);
            --text-color: $(textcolor);
            --card-color: $(cardcolor);
            --border-radius: $(borderradius);
            --shadow-intensity: $(shadowintensity);

            /* ألوان هادئة مشتقة */
            --primary-light: rgba(108, 123, 127, 0.3);
            --primary-dark: rgba(108, 123, 127, 0.8);
            --secondary-light: rgba(149, 165, 166, 0.4);
            --accent-light: rgba(52, 152, 219, 0.3);

            /* تدرجات هادئة */
            --gradient-primary: linear-gradient(135deg, $(keycolor) 0%, $(secondarycolor) 100%);
            --gradient-soft: linear-gradient(135deg, $(backgroundcolor) 0%, #ecf0f1 100%);
            --gradient-accent: linear-gradient(45deg, $(accentcolor) 0%, rgba(52, 152, 219, 0.7) 100%);

            /* ظلال قابلة للتخصيص */
            --shadow-light: 0 4px 15px rgba(0,0,0,$(shadowintensity));
            --shadow-medium: 0 8px 25px rgba(0,0,0,calc($(shadowintensity) + 0.05));
            --shadow-heavy: 0 15px 35px rgba(0,0,0,calc($(shadowintensity) + 0.1));

            /* ألوان حالة هادئة */
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --muted-color: #7f8c8d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: $(body.font);
            line-height: 1.6;
            color: var(--text-color);
            background: var(--background-color);
            overflow-x: hidden;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: $(heading.font);
            color: var(--text-color);
        }

        .hero-section {
            background: var(--gradient-primary);
            background-image:
                radial-gradient(circle at 30% 70%, var(--primary-light) 0%, transparent 60%),
                radial-gradient(circle at 70% 30%, var(--accent-light) 0%, transparent 60%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/><polygon fill="rgba(255,255,255,0.03)" points="0,0 500,500 0,1000"/><polygon fill="rgba(255,255,255,0.03)" points="1000,0 500,500 1000,1000"/></svg>');
            background-size: 200% 200%, cover;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%, center; }
            50% { background-position: 100% 50%, center; }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .download-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1.2rem 2.5rem;
            background:
                linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1)),
                rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .download-btn:hover::before {
            left: 100%;
        }

        .download-btn:hover {
            background:
                linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.2)),
                rgba(255,255,255,0.2);
            transform: translateY(-5px) scale(1.05);
            color: white;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .download-btn:nth-child(1) {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
        }

        .download-btn:nth-child(2) {
            background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
        }

        .download-btn:nth-child(3) {
            background: linear-gradient(45deg, var(--secondary-color), var(--secondary-light));
        }

        .features-section {
            padding: 5rem 0;
            background: var(--gradient-soft);
            position: relative;
        }

        .features-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M0,300 Q250,200 500,300 T1000,300 L1000,0 L0,0 Z" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: cover;
            pointer-events: none;
        }

        .feature-card {
            background: var(--card-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--primary-light);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-medium);
            border-color: var(--accent-color);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: var(--text-color);
        }

        .screenshots-section {
            padding: 5rem 0;
            background: var(--gradient-accent);
            position: relative;
            color: white;
        }

        .screenshots-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .screenshots-section .section-title {
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .screenshot-card {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: all 0.3s ease;
            position: relative;
            background: var(--card-color);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .screenshot-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .screenshot-card:hover::before {
            opacity: 1;
        }

        .screenshot-card:hover {
            transform: translateY(-10px) scale(1.05) rotateY(5deg);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .screenshot-card img {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.3s ease;
        }

        .screenshot-card:hover img {
            transform: scale(1.1);
        }

        .stats-section {
            background: var(--gradient-primary);
            padding: 4rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stats-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .stat-item {
            text-align: center;
            position: relative;
            padding: 2rem 1rem;
            border-radius: var(--border-radius);
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 3.5rem;
            font-weight: 900;
            display: block;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1.2rem;
            opacity: 0.95;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .footer {
            background: var(--text-color);
            color: white;
            padding: 4rem 0 1rem;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            color: white;
            border-radius: var(--border-radius);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--primary-color);
            transform: translateY(-3px);
            color: white;
            box-shadow: var(--shadow-light);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .download-buttons {
                flex-direction: column;
                align-items: center;
                gap: 1.5rem;
            }

            .download-btn {
                width: 100%;
                max-width: 320px;
                padding: 1rem 2rem;
            }

            .floating-element {
                display: none;
            }

            .feature-card {
                margin-bottom: 2rem;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2.2rem;
            }

            .hero-section {
                background-attachment: scroll;
            }
        }

        /* تحسينات إضافية للألوان */
        body {
            background:
                linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        /* تأثيرات التمرير */
        .parallax-element {
            transform: translateZ(0);
            will-change: transform;
        }

        /* تحسين الروابط الاجتماعية بألوان هادئة */
        .social-link:nth-child(1) { background: var(--info-color); }
        .social-link:nth-child(2) { background: var(--accent-color); }
        .social-link:nth-child(3) { background: var(--warning-color); }
        .social-link:nth-child(4) { background: var(--primary-color); }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            opacity: 0.15;
            animation: float 8s ease-in-out infinite;
            color: rgba(255,255,255,0.3);
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }

        .floating-element:nth-child(1) {
            top: 15%;
            left: 8%;
            animation-delay: 0s;
            animation: float 8s ease-in-out infinite, rotate 20s linear infinite;
        }

        .floating-element:nth-child(2) {
            top: 55%;
            right: 8%;
            animation-delay: 2s;
            animation: float 10s ease-in-out infinite, pulse 3s ease-in-out infinite;
        }

        .floating-element:nth-child(3) {
            bottom: 15%;
            left: 15%;
            animation-delay: 4s;
            animation: float 12s ease-in-out infinite, bounce 4s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
            }
            25% {
                transform: translateY(-30px) translateX(10px);
            }
            50% {
                transform: translateY(-20px) translateX(-10px);
            }
            75% {
                transform: translateY(-40px) translateX(5px);
            }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* إضافة تأثيرات إضافية */
        .section-title {
            text-align: center;
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: var(--text-color);
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--accent-color);
            border-radius: var(--border-radius);
        }

        /* تحسين الأكورديون */
        .accordion-button {
            background: var(--card-color);
            border: none;
            border-radius: var(--border-radius) !important;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            color: var(--text-color);
        }

        .accordion-button:hover {
            background: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .accordion-item {
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
        }

        .accordion-body {
            background: var(--card-color);
            color: var(--text-color);
        }

        /* Blogger specific styles */
        .blog-posts {
            display: none;
        }
        
        .sidebar {
            display: none;
        }
    ]]>
    </b:template-skin>
</head>
<body>
    <!-- Hero Section -->
    <section class='hero-section'>
        <div class='floating-elements'>
            <i class='fas fa-futbol floating-element' style='font-size: 4rem;'></i>
            <i class='fas fa-tv floating-element' style='font-size: 3rem;'></i>
            <i class='fas fa-mobile-alt floating-element' style='font-size: 3.5rem;'></i>
        </div>
        
        <div class='container'>
            <div class='row justify-content-center'>
                <div class='col-lg-8'>
                    <div class='hero-content' data-aos='fade-up'>
                        <h1 class='hero-title'>راية كافية</h1>
                        <p class='hero-subtitle'>
                            أفضل تطبيق لمشاهدة المباريات المباشرة والقنوات الرياضية مجاناً<br/>
                            استمتع بمتابعة فريقك المفضل بجودة عالية وبدون انقطاع
                        </p>
                        
                        <div class='download-buttons'>
                            <a class='download-btn' data-aos='fade-up' data-aos-delay='100' href='#'>
                                <i class='fab fa-android'></i>
                                تحميل للأندرويد
                            </a>
                            <a class='download-btn' data-aos='fade-up' data-aos-delay='200' href='#'>
                                <i class='fas fa-desktop'></i>
                                تحميل للكمبيوتر
                            </a>
                            <a class='download-btn' data-aos='fade-up' data-aos-delay='300' href='#'>
                                <i class='fas fa-tv'></i>
                                للتلفاز الذكي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class='stats-section'>
        <div class='container'>
            <div class='row'>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up'>
                        <span class='stat-number'>1M+</span>
                        <span class='stat-label'>مستخدم نشط</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='100'>
                        <span class='stat-number'>500+</span>
                        <span class='stat-label'>قناة متاحة</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='200'>
                        <span class='stat-number'>24/7</span>
                        <span class='stat-label'>بث مستمر</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='300'>
                        <span class='stat-number'>4.8★</span>
                        <span class='stat-label'>تقييم المستخدمين</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class='features-section'>
        <div class='container'>
            <h2 class='section-title' data-aos='fade-up'>مميزات التطبيق</h2>
            <div class='row'>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='100'>
                        <div class='feature-icon'>
                            <i class='fas fa-futbol'></i>
                        </div>
                        <h4>مباريات مباشرة</h4>
                        <p>شاهد جميع المباريات المهمة مباشرة بجودة عالية وبدون انقطاع. تغطية شاملة لجميع الدوريات العالمية والمحلية.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='200'>
                        <div class='feature-icon'>
                            <i class='fas fa-tv'></i>
                        </div>
                        <h4>قنوات متنوعة</h4>
                        <p>أكثر من 500 قناة رياضية وترفيهية وإخبارية. قنوات عربية وعالمية بجودات مختلفة تناسب سرعة الإنترنت لديك.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='300'>
                        <div class='feature-icon'>
                            <i class='fas fa-mobile-alt'></i>
                        </div>
                        <h4>متوافق مع جميع الأجهزة</h4>
                        <p>يعمل على الهواتف الذكية والأجهزة اللوحية والكمبيوتر والتلفاز الذكي. تجربة مشاهدة مثالية على أي جهاز.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='400'>
                        <div class='feature-icon'>
                            <i class='fas fa-hd-video'></i>
                        </div>
                        <h4>جودة عالية</h4>
                        <p>بث بجودة HD و Full HD. إمكانية تغيير الجودة حسب سرعة الإنترنت لضمان مشاهدة سلسة بدون تقطيع.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='500'>
                        <div class='feature-icon'>
                            <i class='fas fa-bell'></i>
                        </div>
                        <h4>تنبيهات المباريات</h4>
                        <p>احصل على تنبيهات قبل بداية المباريات المهمة. لن تفوت أي مباراة لفريقك المفضل مع نظام التنبيهات الذكي.</p>
                    </div>
                </div>
                <div class='col-lg-4 col-md-6 mb-4'>
                    <div class='feature-card' data-aos='fade-up' data-aos-delay='600'>
                        <div class='feature-icon'>
                            <i class='fas fa-language'></i>
                        </div>
                        <h4>تعليق متعدد اللغات</h4>
                        <p>تعليق عربي وإنجليزي وفرنسي وتركي. اختر اللغة التي تفضلها لمتابعة المباريات والبرامج الرياضية.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Screenshots Section -->
    <section class='screenshots-section'>
        <div class='container'>
            <h2 class='section-title' data-aos='fade-up'>لقطات من التطبيق</h2>
            <div class='row'>
                <div class='col-lg-3 col-md-6 mb-4'>
                    <div class='screenshot-card' data-aos='fade-up' data-aos-delay='100'>
                        <img alt='الشاشة الرئيسية' src='https://via.placeholder.com/300x600/1a73e8/ffffff?text=الشاشة+الرئيسية'/>
                    </div>
                </div>
                <div class='col-lg-3 col-md-6 mb-4'>
                    <div class='screenshot-card' data-aos='fade-up' data-aos-delay='200'>
                        <img alt='المباريات المباشرة' src='https://via.placeholder.com/300x600/34a853/ffffff?text=المباريات+المباشرة'/>
                    </div>
                </div>
                <div class='col-lg-3 col-md-6 mb-4'>
                    <div class='screenshot-card' data-aos='fade-up' data-aos-delay='300'>
                        <img alt='القنوات التلفزيونية' src='https://via.placeholder.com/300x600/ea4335/ffffff?text=القنوات+التلفزيونية'/>
                    </div>
                </div>
                <div class='col-lg-3 col-md-6 mb-4'>
                    <div class='screenshot-card' data-aos='fade-up' data-aos-delay='400'>
                        <img alt='الإعدادات' src='https://via.placeholder.com/300x600/fbbc04/ffffff?text=الإعدادات'/>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class='features-section'>
        <div class='container'>
            <h2 class='section-title' data-aos='fade-up'>الأسئلة الشائعة</h2>
            <div class='row justify-content-center'>
                <div class='col-lg-8'>
                    <div class='accordion' id='faqAccordion'>
                        <div class='accordion-item mb-3' data-aos='fade-up' data-aos-delay='100'>
                            <h2 class='accordion-header'>
                                <button class='accordion-button' data-bs-target='#faq1' data-bs-toggle='collapse' type='button'>
                                    هل التطبيق مجاني؟
                                </button>
                            </h2>
                            <div class='accordion-collapse collapse show' data-bs-parent='#faqAccordion' id='faq1'>
                                <div class='accordion-body'>
                                    نعم، تطبيق راية كافية مجاني تماماً. يمكنك تحميله واستخدامه بدون أي رسوم أو اشتراكات.
                                </div>
                            </div>
                        </div>

                        <div class='accordion-item mb-3' data-aos='fade-up' data-aos-delay='200'>
                            <h2 class='accordion-header'>
                                <button class='accordion-button collapsed' data-bs-target='#faq2' data-bs-toggle='collapse' type='button'>
                                    ما هي متطلبات التشغيل؟
                                </button>
                            </h2>
                            <div class='accordion-collapse collapse' data-bs-parent='#faqAccordion' id='faq2'>
                                <div class='accordion-body'>
                                    يتطلب التطبيق نظام أندرويد 4.2 أو أحدث، واتصال إنترنت مستقر. للحصول على أفضل تجربة، ننصح بسرعة إنترنت 2 ميجابت على الأقل.
                                </div>
                            </div>
                        </div>

                        <div class='accordion-item mb-3' data-aos='fade-up' data-aos-delay='300'>
                            <h2 class='accordion-header'>
                                <button class='accordion-button collapsed' data-bs-target='#faq3' data-bs-toggle='collapse' type='button'>
                                    هل يمكن استخدام التطبيق على التلفاز؟
                                </button>
                            </h2>
                            <div class='accordion-collapse collapse' data-bs-parent='#faqAccordion' id='faq3'>
                                <div class='accordion-body'>
                                    نعم، يدعم التطبيق Chromecast ويمكن تشغيله على التلفاز الذكي. كما يتوفر إصدار خاص للتلفاز الذكي.
                                </div>
                            </div>
                        </div>

                        <div class='accordion-item mb-3' data-aos='fade-up' data-aos-delay='400'>
                            <h2 class='accordion-header'>
                                <button class='accordion-button collapsed' data-bs-target='#faq4' data-bs-toggle='collapse' type='button'>
                                    كيف أحل مشكلة التقطيع في البث؟
                                </button>
                            </h2>
                            <div class='accordion-collapse collapse' data-bs-parent='#faqAccordion' id='faq4'>
                                <div class='accordion-body'>
                                    تأكد من قوة الإنترنت، قم بتقليل جودة البث، أعد تشغيل التطبيق، أو امسح ذاكرة التخزين المؤقت للتطبيق.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class='footer'>
        <div class='container'>
            <div class='row'>
                <div class='col-lg-4 mb-4'>
                    <h5>راية كافية</h5>
                    <p>أفضل تطبيق لمشاهدة المباريات المباشرة والقنوات الرياضية. استمتع بمتابعة فريقك المفضل بجودة عالية ومجاناً.</p>
                    <div class='social-links'>
                        <a class='social-link' href='#'><i class='fab fa-facebook-f'></i></a>
                        <a class='social-link' href='#'><i class='fab fa-twitter'></i></a>
                        <a class='social-link' href='#'><i class='fab fa-instagram'></i></a>
                        <a class='social-link' href='#'><i class='fab fa-youtube'></i></a>
                    </div>
                </div>
                <div class='col-lg-2 col-md-6 mb-4'>
                    <h6>روابط سريعة</h6>
                    <ul class='footer-links'>
                        <li><a href='#hero'>الرئيسية</a></li>
                        <li><a href='#features'>المميزات</a></li>
                        <li><a href='#screenshots'>لقطات الشاشة</a></li>
                        <li><a href='#faq'>الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div class='col-lg-2 col-md-6 mb-4'>
                    <h6>التحميل</h6>
                    <ul class='footer-links'>
                        <li><a href='#'>للأندرويد</a></li>
                        <li><a href='#'>للكمبيوتر</a></li>
                        <li><a href='#'>للآيفون</a></li>
                        <li><a href='#'>للتلفاز الذكي</a></li>
                    </ul>
                </div>
                <div class='col-lg-2 col-md-6 mb-4'>
                    <h6>الدعم</h6>
                    <ul class='footer-links'>
                        <li><a href='#'>مركز المساعدة</a></li>
                        <li><a href='#'>اتصل بنا</a></li>
                        <li><a href='#'>الإبلاغ عن مشكلة</a></li>
                        <li><a href='#'>طلب قناة</a></li>
                    </ul>
                </div>
                <div class='col-lg-2 col-md-6 mb-4'>
                    <h6>قانوني</h6>
                    <ul class='footer-links'>
                        <li><a href='#'>سياسة الخصوصية</a></li>
                        <li><a href='#'>الشروط والأحكام</a></li>
                        <li><a href='#'>إخلاء المسؤولية</a></li>
                        <li><a href='#'>حقوق النشر</a></li>
                    </ul>
                </div>
            </div>
            <hr style='border-color: rgba(255,255,255,0.1); margin: 2rem 0 1rem;'/>
            <div class='row'>
                <div class='col-12 text-center'>
                    <p style='margin: 0; opacity: 0.7;'>
                        © 2024 راية كافية. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Hidden Blogger Elements -->
    <div style='display: none;'>
        <b:section class='main' id='main' showaddelement='no'>
            <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='1' visible='true'>
                <b:includable id='main'>
                    <!-- Blog posts will be hidden -->
                </b:includable>
            </b:widget>
        </b:section>
    </div>

    <!-- Bootstrap JS -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>

    <!-- AOS Animation JS -->
    <script src='https://unpkg.com/aos@2.3.1/dist/aos.js'></script>

    <!-- Custom JavaScript -->
    <script>
    //<![CDATA[
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Counter animation for stats
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = counter.innerText;
                const numericTarget = parseInt(target.replace(/[^\d]/g, ''));
                const suffix = target.replace(/[\d]/g, '');

                let current = 0;
                const increment = numericTarget / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numericTarget) {
                        current = numericTarget;
                        clearInterval(timer);
                    }
                    counter.innerText = Math.floor(current) + suffix;
                }, 20);
            });
        }

        // Trigger counter animation when stats section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }
    //]]>
    </script>
</body>
</html>
