<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    <title>راية كافية - معاينة القالب المحسن</title>
    
    <!-- Google Fonts -->
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap' rel='stylesheet'/>
    
    <!-- Font Awesome -->
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    
    <!-- Bootstrap CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    
    <!-- AOS Animation Library -->
    <link href='https://unpkg.com/aos@2.3.1/dist/aos.css' rel='stylesheet'/>
    
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #ea4335;
            --warning-color: #ff9800;
            --success-color: #4caf50;
            --info-color: #2196f3;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --gradient-primary: linear-gradient(135deg, #1a73e8 0%, #34a853 100%);
            --gradient-secondary: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            --gradient-sunset: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
            --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-fire: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            --shadow-light: 0 4px 15px rgba(0,0,0,0.1);
            --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
            --shadow-heavy: 0 15px 35px rgba(0,0,0,0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            overflow-x: hidden;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .hero-section {
            background: var(--gradient-primary);
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/><polygon fill="rgba(255,255,255,0.03)" points="0,0 500,500 0,1000"/><polygon fill="rgba(255,255,255,0.03)" points="1000,0 500,500 1000,1000"/></svg>');
            background-size: 200% 200%, cover;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%, center; }
            50% { background-position: 100% 50%, center; }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .download-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1.2rem 2.5rem;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .download-btn:nth-child(1) {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .download-btn:nth-child(2) {
            background: linear-gradient(45deg, #4834d4, #686de0);
        }

        .download-btn:nth-child(3) {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
        }

        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .download-btn:hover::before {
            left: 100%;
        }

        .download-btn:hover {
            transform: translateY(-5px) scale(1.05);
            color: white;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            opacity: 0.15;
            animation: float 8s ease-in-out infinite;
            color: rgba(255,255,255,0.3);
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }

        .floating-element:nth-child(1) {
            top: 15%;
            left: 8%;
            animation: float 8s ease-in-out infinite, rotate 20s linear infinite;
        }

        .floating-element:nth-child(2) {
            top: 55%;
            right: 8%;
            animation: float 10s ease-in-out infinite, pulse 3s ease-in-out infinite;
        }

        .floating-element:nth-child(3) {
            bottom: 15%;
            left: 15%;
            animation: float 12s ease-in-out infinite, bounce 4s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
            }
            25% {
                transform: translateY(-30px) translateX(10px);
            }
            50% {
                transform: translateY(-20px) translateX(-10px);
            }
            75% {
                transform: translateY(-40px) translateX(5px);
            }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .stats-section {
            background: 
                var(--gradient-sunset),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="waves" width="100" height="20" patternUnits="userSpaceOnUse"><path d="M0 10 Q 25 0 50 10 T 100 10 V 20 H 0 Z" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23waves)"/></svg>');
            padding: 4rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stat-item {
            text-align: center;
            position: relative;
            padding: 2rem 1rem;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 3.5rem;
            font-weight: 900;
            display: block;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1.2rem;
            opacity: 0.95;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
                gap: 1.5rem;
            }
            
            .download-btn {
                width: 100%;
                max-width: 320px;
            }

            .floating-element {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class='hero-section'>
        <div class='floating-elements'>
            <i class='fas fa-futbol floating-element' style='font-size: 4rem;'></i>
            <i class='fas fa-tv floating-element' style='font-size: 3rem;'></i>
            <i class='fas fa-mobile-alt floating-element' style='font-size: 3.5rem;'></i>
        </div>
        
        <div class='container'>
            <div class='row justify-content-center'>
                <div class='col-lg-8'>
                    <div class='hero-content' data-aos='fade-up'>
                        <h1 class='hero-title'>راية كافية</h1>
                        <p class='hero-subtitle'>
                            أفضل تطبيق لمشاهدة المباريات المباشرة والقنوات الرياضية مجاناً<br/>
                            استمتع بمتابعة فريقك المفضل بجودة عالية وبدون انقطاع
                        </p>
                        
                        <div class='download-buttons'>
                            <a class='download-btn' href='#'>
                                <i class='fab fa-android'></i>
                                تحميل للأندرويد
                            </a>
                            <a class='download-btn' href='#'>
                                <i class='fas fa-desktop'></i>
                                تحميل للكمبيوتر
                            </a>
                            <a class='download-btn' href='#'>
                                <i class='fas fa-tv'></i>
                                للتلفاز الذكي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class='stats-section'>
        <div class='container'>
            <div class='row'>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up'>
                        <span class='stat-number'>1M+</span>
                        <span class='stat-label'>مستخدم نشط</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='100'>
                        <span class='stat-number'>500+</span>
                        <span class='stat-label'>قناة متاحة</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='200'>
                        <span class='stat-number'>24/7</span>
                        <span class='stat-label'>بث مستمر</span>
                    </div>
                </div>
                <div class='col-md-3 col-6 mb-3'>
                    <div class='stat-item' data-aos='fade-up' data-aos-delay='300'>
                        <span class='stat-number'>4.8★</span>
                        <span class='stat-label'>تقييم المستخدمين</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    
    <!-- AOS Animation JS -->
    <script src='https://unpkg.com/aos@2.3.1/dist/aos.js'></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>
</body>
</html>
