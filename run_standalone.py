#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مستقل للبرنامج
Standalone runner for Live TV Viewer
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """التحقق من المتطلبات"""
    missing_modules = []
    
    # قائمة المكتبات المطلوبة
    required_modules = [
        ('requests', 'requests'),
        ('tkinter', 'tkinter'),
        ('json', 'json'),
        ('threading', 'threading'),
        ('subprocess', 'subprocess'),
        ('datetime', 'datetime'),
        ('hashlib', 'hashlib')
    ]
    
    for module_name, import_name in required_modules:
        try:
            __import__(import_name)
        except ImportError:
            missing_modules.append(module_name)
    
    return missing_modules

def install_missing_modules(modules):
    """تثبيت المكتبات المفقودة"""
    if not modules:
        return True
    
    print("🔧 تثبيت المكتبات المفقودة...")
    
    for module in modules:
        try:
            print(f"📦 تثبيت {module}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", module
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ تم تثبيت {module}")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {module}")
            return False
    
    return True

def create_error_dialog(title, message):
    """إنشاء نافذة خطأ"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    messagebox.showerror(title, message)
    root.destroy()

def run_main_app():
    """تشغيل البرنامج الرئيسي"""
    try:
        # التحقق من وجود الملف الرئيسي
        if not os.path.exists("live_tv_app.py"):
            create_error_dialog("خطأ", "الملف الرئيسي غير موجود: live_tv_app.py")
            return False
        
        # تشغيل البرنامج
        print("🚀 تشغيل البرنامج الرئيسي...")
        
        # استيراد وتشغيل البرنامج
        import live_tv_app
        app = live_tv_app.LiveTVApp()
        app.root.mainloop()
        
        return True
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات:\n{str(e)}\n\nتأكد من تثبيت جميع المتطلبات"
        create_error_dialog("خطأ في الاستيراد", error_msg)
        return False
    except Exception as e:
        error_msg = f"خطأ في تشغيل البرنامج:\n{str(e)}"
        create_error_dialog("خطأ في التشغيل", error_msg)
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔴 مشاهد القنوات المباشرة - Live TV Viewer")
    print("=" * 60)
    print()
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 6):
        error_msg = f"يتطلب Python 3.6 أو أحدث\nالإصدار الحالي: {sys.version}"
        print(f"❌ {error_msg}")
        create_error_dialog("إصدار Python غير مدعوم", error_msg)
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # التحقق من المتطلبات
    print("🔍 التحقق من المتطلبات...")
    missing_modules = check_requirements()
    
    if missing_modules:
        print(f"⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
        
        # محاولة التثبيت التلقائي
        install_choice = input("هل تريد تثبيت المكتبات المفقودة تلقائياً؟ (y/n): ").lower().strip()
        
        if install_choice == 'y':
            if not install_missing_modules(missing_modules):
                error_msg = "فشل في تثبيت بعض المكتبات المطلوبة"
                print(f"❌ {error_msg}")
                create_error_dialog("خطأ في التثبيت", error_msg)
                return False
        else:
            error_msg = f"المكتبات المطلوبة غير مثبتة:\n{chr(10).join(missing_modules)}"
            print(f"❌ {error_msg}")
            create_error_dialog("مكتبات مفقودة", error_msg)
            return False
    
    print("✅ جميع المتطلبات متوفرة")
    print()
    
    # تشغيل البرنامج
    return run_main_app()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n❌ اضغط Enter للخروج...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء التشغيل بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        create_error_dialog("خطأ غير متوقع", str(e))
        input("اضغط Enter للخروج...")
        sys.exit(1)
