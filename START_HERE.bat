@echo off
chcp 65001 >nul
title Live TV Viewer - البدء السريع
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        🔴 مشاهد القنوات المباشرة - Live TV Viewer        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🚀 مرحباً بك في برنامج مشاهدة القنوات المباشرة
echo.
echo 📋 اختر ما تريد فعله:
echo.
echo    1️⃣  إعداد البرنامج لأول مرة
echo    2️⃣  تشغيل البرنامج مباشرة
echo    3️⃣  اختبار البرنامج
echo    4️⃣  بناء ملف EXE
echo    5️⃣  تشخيص مشاكل الحفظ
echo    6️⃣  عرض التعليمات
echo    7️⃣  خروج
echo.
set /p choice="👆 اختر رقم (1-7): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto run
if "%choice%"=="3" goto test
if "%choice%"=="4" goto build
if "%choice%"=="5" goto diagnose
if "%choice%"=="6" goto help
if "%choice%"=="7" goto exit
goto invalid

:setup
echo.
echo 🔧 جاري إعداد البرنامج...
python setup.py
pause
goto menu

:run
echo.
echo ▶️ جاري تشغيل البرنامج...
echo.

REM التحقق من وجود ملف EXE أولاً
if exist "LiveTVViewer_Portable\LiveTVViewer.exe" (
    echo 🚀 تم العثور على الإصدار المحمول، جاري التشغيل...
    cd LiveTVViewer_Portable
    LiveTVViewer.exe
    cd ..
    goto menu
)

REM التحقق من وجود ملف EXE في مجلد dist
if exist "dist\LiveTVViewer.exe" (
    echo 🚀 تم العثور على ملف EXE، جاري التشغيل...
    dist\LiveTVViewer.exe
    goto menu
)

REM تشغيل بـ Python مع التحقق من المتطلبات
echo 🐍 تشغيل بـ Python...
if exist "run_standalone.py" (
    python run_standalone.py
) else (
    python live_tv_app.py
)

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في التشغيل
    echo.
    echo � حلول مقترحة:
    echo    1. تشغيل الإعداد أولاً (الخيار 1)
    echo    2. بناء ملف EXE (الخيار 4)
    echo    3. التحقق من تثبيت Python
    echo.
    pause
)
goto menu

:test
echo.
echo 🧪 جاري اختبار البرنامج...
python test_app.py
pause
goto menu

:build
echo.
echo 🏗️ جاري بناء ملف EXE...
python build.py
pause
goto menu

:diagnose
echo.
echo 🩺 تشخيص مشاكل الحفظ...
echo.
echo 📋 اختر نوع التشخيص:
echo.
echo    1. تشخيص سريع
echo    2. اختبار حفظ القنوات
echo    3. كلاهما
echo.
set /p diag_choice="اختر (1-3): "

if "%diag_choice%"=="1" (
    echo.
    echo 🔍 تشغيل التشخيص السريع...
    python diagnose_save_issue.py
) else if "%diag_choice%"=="2" (
    echo.
    echo 🧪 تشغيل اختبار حفظ القنوات...
    python test_channels_save.py
) else if "%diag_choice%"=="3" (
    echo.
    echo 🔍 تشغيل التشخيص السريع...
    python diagnose_save_issue.py
    echo.
    echo 🧪 تشغيل اختبار حفظ القنوات...
    python test_channels_save.py
) else (
    echo.
    echo ❌ اختيار غير صحيح
)
pause
goto menu

:help
echo.
echo 📖 التعليمات السريعة:
echo.
echo 🔐 أكواد الوصول:
echo    - كود التفعيل: TEST123
echo    - كود الإدمن: ADMIN2024
echo.
echo 📋 خطوات الاستخدام:
echo    1. تشغيل البرنامج
echo    2. إدخال كود التفعيل
echo    3. اختيار قناة
echo    4. بدء المشاهدة
echo.
echo 🛠️ المتطلبات:
echo    - Python 3.6+
echo    - VLC Media Player (اختياري)
echo    - اتصال بالإنترنت
echo.
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح
echo.
pause

:menu
cls
goto start

:exit
echo.
echo 👋 شكراً لاستخدام Live TV Viewer
echo.
exit /b 0

:start
goto menu
