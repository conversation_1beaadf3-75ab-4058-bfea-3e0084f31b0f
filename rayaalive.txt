import tkinter as tk
from tkinter import messagebox
import threading
import time
import requests

class KeepAliveApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تطبيق إبقاء الروابط مفعلة")

        # واجهة المستخدم
        tk.Label(root, text="رابط القناة (.m3u8):").pack()
        self.m3u8_entry = tk.Entry(root, width=50)
        self.m3u8_entry.pack()

        tk.Label(root, text="رابط التفعيل (.js):").pack()
        self.js_entry = tk.Entry(root, width=50)
        self.js_entry.pack()

        tk.Label(root, text="الفاصل الزمني (بالثواني):").pack()
        self.interval_entry = tk.Entry(root, width=10)
        self.interval_entry.insert(0, "60")
        self.interval_entry.pack()

        self.start_button = tk.Button(root, text="ابدأ", command=self.start_sending)
        self.start_button.pack(pady=10)

        self.running = False

    def send_js_link(self):
        while self.running:
            try:
                url = self.js_url
                print(f"إرسال رابط التفعيل: {url}")
                response = requests.get(url, timeout=10)
                print("تم الإرسال، الحالة:", response.status_code)
            except Exception as e:
                print("خطأ:", e)
            time.sleep(self.interval)

    def start_sending(self):
        self.js_url = self.js_entry.get().strip()
        self.m3u8_url = self.m3u8_entry.get().strip()
        try:
            self.interval = int(self.interval_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "أدخل رقمًا صحيحًا للفاصل الزمني.")
            return

        if not self.js_url or not self.m3u8_url:
            messagebox.showerror("خطأ", "يرجى ملء الحقول كلها.")
            return

        self.running = True
        threading.Thread(target=self.send_js_link, daemon=True).start()
        self.start_button.config(state=tk.DISABLED)
        messagebox.showinfo("بدء", "بدأ التطبيق بإرسال رابط التفعيل تلقائيًا.")

# تشغيل التطبيق
if __name__ == "__main__":
    root = tk.Tk()
    app = KeepAliveApp(root)
    root.mainloop()
