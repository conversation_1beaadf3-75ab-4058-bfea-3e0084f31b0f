# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.abspath('.'))

# تحديد الملفات المخفية المطلوبة
hiddenimports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.simpledialog',
    'tkinter.filedialog',
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.models',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    'urllib3',
    'urllib3.util',
    'urllib3.util.retry',
    'urllib3.util.connection',
    'urllib3.connection',
    'urllib3.connectionpool',
    'urllib3.poolmanager',
    'urllib3.response',
    'json',
    'threading',
    'subprocess',
    'os',
    'sys',
    'time',
    'datetime',
    'hashlib',
    'random',
    'string',
    'tempfile',
    'shutil',
    'platform',
    'webbrowser'
]

# تحديد البيانات المطلوبة
datas = [
    ('data.json', '.'),
]

# إضافة ملفات إضافية إذا كانت موجودة
additional_files = ['config.json', 'README.md', 'QUICK_START.md']
for file in additional_files:
    if os.path.exists(file):
        datas.append((file, '.'))

# تحليل الملف الرئيسي
a = Analysis(
    ['live_tv_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'sklearn'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المطلوبة
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

# إنشاء ملف PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='LiveTVViewer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # بدون نافذة console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
