# 🛠️ دليل حل المشاكل - Live TV Viewer

## 🔧 المشاكل الشائعة والحلول

### 1. 💾 مشكلة عدم حفظ الإعدادات من لوحة الإدمن

#### الأعراض:
- تغيير الإعدادات في لوحة الإدمن
- عدم حفظ التغييرات بعد إعادة تشغيل البرنامج
- فقدان القنوات المضافة

#### الحلول:

**الحل الأول: التحقق من صلاحيات الملفات**
```
1. تشغيل البرنامج كمدير (Run as Administrator)
2. التأكد من وجود صلاحيات الكتابة في مجلد البرنامج
3. التحقق من عدم حماية الملفات بواسطة مكافح الفيروسات
```

**الحل الثاني: الحفظ اليدوي**
```
1. في لوحة الإدمن → الضغط على "حفظ التغييرات"
2. التحقق من ظهور رسالة "تم الحفظ بنجاح"
3. التحقق من وجود ملف data.json محدث
```

**الحل الثالث: استخدام النسخ الاحتياطية**
```
1. البحث عن ملفات data_backup_*.json
2. نسخ أحدث ملف احتياطي
3. إعادة تسميته إلى data.json
```

### 2. 🐍 مشكلة الحاجة لـ Python

#### الأعراض:
- البرنامج لا يعمل بدون Python
- رسالة "Python غير مثبت"
- عدم وجود ملف EXE

#### الحلول:

**الحل الأول: بناء ملف EXE**
```
1. تشغيل START_HERE.bat
2. اختيار "4" لبناء ملف EXE
3. انتظار انتهاء عملية البناء
4. استخدام الملف من مجلد LiveTVViewer_Portable
```

**الحل الثاني: تثبيت Python**
```
1. تحميل Python من https://python.org
2. تثبيت مع تفعيل "Add to PATH"
3. إعادة تشغيل الكمبيوتر
4. تشغيل البرنامج
```

**الحل الثالث: استخدام الإصدار المحمول**
```
1. تحميل الإصدار المحمول الجاهز
2. فك الضغط في مجلد منفصل
3. تشغيل LiveTVViewer.exe مباشرة
```

### 3. 📺 مشكلة عدم تشغيل القنوات

#### الأعراض:
- القنوات تظهر في القائمة
- عدم تشغيل عند الضغط على "ابدأ المشاهدة"
- رسائل خطأ في التشغيل

#### الحلول:

**الحل الأول: تثبيت VLC**
```
1. تحميل VLC من https://videolan.org
2. تثبيت في المسار الافتراضي
3. إعادة تشغيل البرنامج
```

**الحل الثاني: التحقق من روابط القنوات**
```
1. الدخول للوحة الإدمن
2. التحقق من صحة روابط M3U8
3. اختبار الروابط في متصفح الويب
```

**الحل الثالث: التحقق من التفعيل**
```
1. التأكد من عمل نظام التفعيل
2. التحقق من رسائل الحالة
3. إعادة تشغيل القناة
```

### 4. 🔐 مشكلة أكواد التفعيل

#### الأعراض:
- رفض كود التفعيل الصحيح
- عدم قبول كود الإدمن
- انتهاء صلاحية الكود

#### الحلول:

**الأكواد الافتراضية:**
```
- كود التفعيل: TEST123
- كود الإدمن: ADMIN2024
```

**إعادة تعيين الأكواد:**
```
1. حذف ملف data.json
2. إعادة تشغيل البرنامج
3. سيتم إنشاء ملف جديد بالأكواد الافتراضية
```

### 5. 🌐 مشكلة الاتصال بالإنترنت

#### الأعراض:
- فشل في تحميل البيانات
- عدم عمل التفعيل التلقائي
- رسائل خطأ الشبكة

#### الحلول:

**التحقق من الاتصال:**
```
1. التأكد من وجود اتصال إنترنت
2. التحقق من إعدادات الجدار الناري
3. تجربة تعطيل مكافح الفيروسات مؤقتاً
```

**استخدام البيانات المحلية:**
```
1. التأكد من وجود ملف data.json
2. تحديث البيانات يدوياً
3. استخدام الوضع المحلي
```

## 🔍 تشخيص المشاكل

### فحص الملفات المطلوبة:
```
✅ live_tv_app.py - الملف الرئيسي
✅ data.json - بيانات القنوات والأكواد
✅ requirements.txt - قائمة المكتبات
✅ README.md - دليل الاستخدام
```

### فحص المكتبات:
```
python -c "import tkinter, requests, json; print('جميع المكتبات متوفرة')"
```

### فحص صلاحيات الملفات:
```
1. النقر بالزر الأيمن على مجلد البرنامج
2. خصائص → الأمان
3. التأكد من وجود صلاحيات الكتابة
```

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم:
```
- إصدار Windows
- إصدار Python (إن وجد)
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
```

### ملفات السجلات:
```
- البحث عن ملفات .log في مجلد البرنامج
- نسخ محتوى نافذة الأخطاء
- لقطة شاشة للمشكلة
```

## 🚀 نصائح للأداء الأفضل

### تحسين الأداء:
```
1. استخدام الإصدار المحمول (EXE)
2. إغلاق البرامج غير المطلوبة
3. التأكد من سرعة الإنترنت
4. استخدام VLC للتشغيل
```

### الصيانة الدورية:
```
1. تحديث البيانات أسبوعياً
2. حذف الملفات الاحتياطية القديمة
3. التحقق من تحديثات البرنامج
4. إعادة تشغيل البرنامج يومياً
```

---

**💡 تذكر:** معظم المشاكل يمكن حلها بإعادة تشغيل البرنامج أو استخدام الإصدار المحمول!
